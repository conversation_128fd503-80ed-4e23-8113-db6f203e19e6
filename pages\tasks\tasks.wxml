<!--tasks.wxml-->
<view class="container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item {{currentFilter === 'all' ? 'active' : ''}}" bindtap="setFilter" data-filter="all">
      全部 ({{stats.total}})
    </view>
    <view class="filter-item {{currentFilter === 'pending' ? 'active' : ''}}" bindtap="setFilter" data-filter="pending">
      待完成 ({{stats.pending}})
    </view>
    <view class="filter-item {{currentFilter === 'completed' ? 'active' : ''}}" bindtap="setFilter" data-filter="completed">
      已完成 ({{stats.completed}})
    </view>
  </view>

  <!-- 任务列表 -->
  <view wx:if="{{filteredTasks.length > 0}}">
    <view class="task-item list-item" wx:for="{{filteredTasks}}" wx:key="id">
      <view class="task-checkbox" bindtap="toggleTask" data-id="{{item.id}}">
        <view class="checkbox {{item.status === 'completed' ? 'checked' : ''}}">
          <text wx:if="{{item.status === 'completed'}}">✓</text>
        </view>
      </view>
      
      <view class="task-content" bindtap="goToTaskDetail" data-id="{{item.id}}">
        <view class="list-item-header">
          <text class="list-item-title {{item.status === 'completed' ? 'completed' : ''}}">{{item.title}}</text>
          <view class="status-tag status-{{item.priority}}">{{item.priorityText}}</view>
        </view>
        
        <text class="list-item-content" wx:if="{{item.description}}">{{item.description}}</text>
        
        <view class="list-item-meta">
          <text class="task-due-date {{item.isOverdue ? 'overdue' : ''}}">
            {{item.dueDateText}}
          </text>
          <view class="task-actions">
            <text class="action-btn" bindtap="editTask" data-id="{{item.id}}">编辑</text>
            <text class="action-btn delete" bindtap="deleteTask" data-id="{{item.id}}">删除</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <text class="empty-icon">📝</text>
    <text class="empty-text">暂无任务</text>
    <text class="empty-desc">点击右下角按钮添加新任务</text>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab" bindtap="goToAddTask">+</view>
</view>