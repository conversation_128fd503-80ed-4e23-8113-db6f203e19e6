<!--index.wxml-->
<view class="container">
  <!-- 欢迎卡片 -->
  <view class="card welcome-card">
    <view class="welcome-header">
      <view class="avatar-container">
        <image class="avatar-small" src="{{userInfo.avatarUrl || '/images/default-avatar.svg'}}" mode="aspectFill" bindtap="getUserProfile"></image>
        <text class="update-hint" bindtap="getUserProfile">点击获取微信信息</text>
      </view>
      <view class="welcome-text">
        <text class="welcome-title">你好，{{userInfo.nickName || '微信用户'}}</text>
        <text class="welcome-subtitle">今天是 {{todayDate}}</text>
      </view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-grid">
    <view class="stat-card card">
      <view class="stat-number text-primary">{{stats.totalTasks}}</view>
      <view class="stat-label">总任务</view>
    </view>
    <view class="stat-card card">
      <view class="stat-number text-warning">{{stats.pendingTasks}}</view>
      <view class="stat-label">待完成</view>
    </view>
    <view class="stat-card card">
      <view class="stat-number text-success">{{stats.completedTasks}}</view>
      <view class="stat-label">已完成</view>
    </view>
    <view class="stat-card card">
      <view class="stat-number text-danger">{{stats.overdueTasks}}</view>
      <view class="stat-label">已逾期</view>
    </view>
  </view>

  <!-- 今日任务 -->
  <view class="card">
    <view class="card-header">
      <text class="card-title">今日任务</text>
      <text class="text-primary" bindtap="goToTasks">查看全部</text>
    </view>
    <view wx:if="{{todayTasks.length > 0}}">
      <view class="task-item" wx:for="{{todayTasks}}" wx:key="id" bindtap="goToTaskDetail" data-id="{{item.id}}">
        <view class="task-header">
          <text class="task-title">{{item.title}}</text>
          <view class="status-tag status-{{item.priority}}">{{item.priorityText}}</view>
        </view>
        <text class="task-desc">{{item.description}}</text>
        <view class="task-meta">
          <text class="task-time">{{item.dueTime}}</text>
          <view class="status-tag status-{{item.status}}">{{item.statusText}}</view>
        </view>
      </view>
    </view>
    <view wx:else class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-text">今天暂无任务</text>
    </view>
  </view>

  <!-- 最近日程 -->
  <view class="card">
    <view class="card-header">
      <text class="card-title">最近日程</text>
      <text class="text-primary" bindtap="goToSchedule">查看全部</text>
    </view>
    <view wx:if="{{recentSchedules.length > 0}}">
      <view class="schedule-item" wx:for="{{recentSchedules}}" wx:key="id">
        <view class="schedule-time">{{item.time}}</view>
        <view class="schedule-content">
          <text class="schedule-title">{{item.title}}</text>
          <text class="schedule-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
    <view wx:else class="empty-state">
      <text class="empty-icon">📅</text>
      <text class="empty-text">暂无日程安排</text>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="quick-item" bindtap="goToAddTask">
      <view class="quick-icon">➕</view>
      <text class="quick-text">添加任务</text>
    </view>
    <view class="quick-item" bindtap="goToAddSchedule">
      <view class="quick-icon">📅</view>
      <text class="quick-text">添加日程</text>
    </view>
    <view class="quick-item" bindtap="goToAddNote">
      <view class="quick-icon">📝</view>
      <text class="quick-text">添加备忘</text>
    </view>
  </view>
</view>