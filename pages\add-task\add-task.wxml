<!--add-task.wxml-->
<view class="container">
  <view class="header">
    <text class="title">{{isEdit ? '编辑任务' : '添加任务'}}</text>
  </view>

  <view class="form">
    <!-- 任务标题 -->
    <view class="form-item">
      <text class="label">任务标题 *</text>
      <input 
        class="input" 
        placeholder="请输入任务标题" 
        value="{{task.title}}"
        bindinput="onTitleInput"
        maxlength="50"
      />
    </view>

    <!-- 任务描述 -->
    <view class="form-item">
      <text class="label">任务描述</text>
      <textarea 
        class="textarea" 
        placeholder="请输入任务描述（可选）" 
        value="{{task.description}}"
        bindinput="onDescriptionInput"
        maxlength="200"
        auto-height
      />
    </view>

    <!-- 优先级 -->
    <view class="form-item">
      <text class="label">优先级</text>
      <view class="priority-options">
        <view 
          wx:for="{{priorityOptions}}" 
          wx:key="value"
          class="priority-option {{task.priority === item.value ? 'active' : ''}}"
          style="border-color: {{item.color}}; color: {{task.priority === item.value ? item.color : '#666'}}"
          data-priority="{{item.value}}"
          bindtap="onPriorityChange"
        >
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>

    <!-- 截止日期 -->
    <view class="form-item">
      <text class="label">截止日期 *</text>
      <picker 
        mode="date" 
        value="{{task.dueDate}}" 
        bindchange="onDateChange"
        start="{{today}}"
      >
        <view class="picker">
          <text class="{{task.dueDate ? '' : 'placeholder'}}">
            {{task.dueDate || '请选择截止日期'}}
          </text>
        </view>
      </picker>
    </view>

    <!-- 截止时间 -->
    <view class="form-item">
      <text class="label">截止时间</text>
      <picker 
        mode="time" 
        value="{{task.dueTime}}" 
        bindchange="onTimeChange"
      >
        <view class="picker">
          <text>{{task.dueTime}}</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="btn btn-cancel" bindtap="cancel">取消</button>
    <button class="btn btn-save" bindtap="saveTask">{{isEdit ? '更新' : '保存'}}</button>
  </view>
</view>