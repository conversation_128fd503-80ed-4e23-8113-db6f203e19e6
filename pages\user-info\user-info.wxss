/* user-info.wxss */

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 0 40rpx 0;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 0;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 表单卡片 */
.form-card {
  margin-bottom: 24rpx;
}

.form-section {
  margin-bottom: 40rpx;
}

/* 头像设置区域 */
.avatar-section {
  display: flex;
  justify-content: center;
  margin: 24rpx 0;
}

.avatar-btn {
  position: relative;
  padding: 0;
  border: none;
  background: none;
  border-radius: 0;
}

.avatar-btn::after {
  border: none;
}

.avatar-preview {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #e9ecef;
  display: block;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.avatar-text {
  color: white;
  font-size: 24rpx;
}

/* 头像操作区域 */
.avatar-actions {
  display: flex;
  justify-content: center;
  margin-top: 24rpx;
}

/* 昵称输入框 */
.nickname-input {
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
}

/* 表单描述文字 */
.form-desc {
  font-size: 26rpx;
  color: #666;
  margin-top: 12rpx;
  display: block;
  text-align: center;
  line-height: 1.4;
}

/* 表单操作按钮 */
.form-actions {
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.save-btn {
  width: 100%;
  margin: 0;
}

/* 提示卡片 */
.tip-card {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border: 2rpx solid #2196f3;
}

.tip-content {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.tip-icon {
  font-size: 48rpx;
  line-height: 1;
}

.tip-text {
  flex: 1;
}

.tip-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1976d2;
  display: block;
  margin-bottom: 12rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #1565c0;
  line-height: 1.5;
}
