// user-info.js
const app = getApp()
const { showSuccess, showError } = require('../../utils/common')

Page({
  data: {
    userInfo: {
      nickName: '微信用户',
      avatarUrl: '/images/default-avatar.svg'
    }
  },

  onLoad() {
    // 获取当前用户信息
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo') || {
      nickName: '微信用户',
      avatarUrl: '/images/default-avatar.svg'
    }
    
    this.setData({
      userInfo: userInfo
    })
  },

  // 选择头像（微信小程序新版本方式）
  onChooseAvatar(e) {
    try {
      const { avatarUrl } = e.detail
      console.log('选择头像成功:', avatarUrl)
      
      if (!avatarUrl) {
        showError('头像选择失败，请重试')
        return
      }
      
      const userInfo = {
        ...this.data.userInfo,
        avatarUrl: avatarUrl
      }
      
      this.setData({
        userInfo: userInfo
      })
      
      // 保存到全局和本地存储
      app.globalData.userInfo = userInfo
      wx.setStorageSync('userInfo', userInfo)
      
      showSuccess('头像更新成功')
    } catch (error) {
      console.error('头像选择异常:', error)
      showError('头像选择失败')
    }
  },
  
  // 备用的头像选择方法
  chooseAvatarFallback() {
    const that = this
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        
        const userInfo = {
          ...that.data.userInfo,
          avatarUrl: tempFilePath
        }
        
        that.setData({
          userInfo: userInfo
        })
        
        app.globalData.userInfo = userInfo
        wx.setStorageSync('userInfo', userInfo)
        
        showSuccess('头像更新成功')
      },
      fail: (err) => {
        console.error('选择头像失败:', err)
        showError('选择头像失败')
      }
    })
  },

  // 昵称输入
  onNicknameInput(e) {
    const nickName = e.detail.value
    const userInfo = {
      ...this.data.userInfo,
      nickName: nickName
    }
    
    this.setData({
      userInfo: userInfo
    })
  },

  // 昵称输入完成
  onNicknameBlur() {
    const { userInfo } = this.data
    
    if (!userInfo.nickName || !userInfo.nickName.trim()) {
      this.setData({
        'userInfo.nickName': '微信用户'
      })
      return
    }
    
    // 保存到全局和本地存储
    const updatedUserInfo = {
      ...userInfo,
      nickName: userInfo.nickName.trim()
    }
    
    app.globalData.userInfo = updatedUserInfo
    wx.setStorageSync('userInfo', updatedUserInfo)
    
    this.setData({
      userInfo: updatedUserInfo
    })
  },

  // 保存用户信息
  saveUserInfo() {
    const { userInfo } = this.data
    
    if (!userInfo.nickName.trim()) {
      showError('请输入昵称')
      return
    }
    
    // 保存到全局和本地存储
    app.globalData.userInfo = userInfo
    wx.setStorageSync('userInfo', userInfo)
    
    showSuccess('用户信息保存成功')
    
    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})
