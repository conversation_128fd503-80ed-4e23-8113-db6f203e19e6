# 微信登录功能使用指南

## 功能概述

本项目已集成完整的微信登录功能，支持多种登录方式：

1. **微信一键登录**（推荐）- 最简单快捷的登录方式
2. **微信授权登录** - 传统的微信授权方式
3. **游客模式** - 无需登录，功能有限

## 登录方式说明

### 1. 微信一键登录
- **特点**: 智能检测授权状态，自动选择最佳登录方式
- **优势**: 用户体验最好，登录流程最简单
- **实现**: 使用 `quickWechatLogin()` 方法

### 2. 微信授权登录
- **特点**: 传统的微信用户信息授权方式
- **适用**: 需要明确获取用户授权的场景
- **实现**: 使用 `wechatLogin()` 方法

### 3. 游客模式
- **特点**: 无需微信授权，可直接使用基础功能
- **限制**: 数据无法同步，功能受限
- **实现**: 使用 `guestLogin()` 方法

## 技术实现

### 核心文件

1. **utils/wechat-auth.js** - 微信登录工具类
   - 统一管理所有微信登录相关功能
   - 智能选择最佳的用户信息获取方式
   - 完善的错误处理和状态管理

2. **pages/login/login.js** - 登录页面逻辑
   - 调用微信登录工具类
   - 处理用户交互和界面更新

3. **utils/api.js** - 后端API接口（可选）
   - 封装与后端服务的通信
   - 支持用户数据同步

### 登录流程

```javascript
// 微信一键登录流程
1. 检查微信session状态
2. 获取微信登录code
3. 智能获取用户信息（根据授权状态选择方式）
4. 调用后端接口（可选）
5. 保存用户信息和登录状态
6. 跳转到应用首页
```

### 用户信息存储

用户信息包含以下字段：
```javascript
{
  nickName: '用户昵称',
  avatarUrl: '头像URL',
  gender: 0, // 性别
  country: '国家',
  province: '省份', 
  city: '城市',
  language: 'zh_CN',
  loginTime: 1234567890, // 登录时间戳
  loginType: 'wechat', // 登录类型
  loginCode: 'wx_code', // 微信登录code
  openid: 'user_openid', // 用户openid（从后端获取）
  unionid: 'user_unionid', // 用户unionid（从后端获取）
  isGuest: false // 是否为游客用户
}
```

## 使用方法

### 在页面中使用

```javascript
// 引入微信登录工具
const { wechatAuth } = require('../../utils/wechat-auth.js')

// 检查登录状态
const isLoggedIn = await wechatAuth.validateLogin()

// 执行登录
try {
  const userInfo = await wechatAuth.login()
  console.log('登录成功:', userInfo)
} catch (error) {
  console.error('登录失败:', error)
}
```

### 在app.js中使用

```javascript
// 检查是否需要登录
if (!app.requireLogin()) {
  return // 未登录，已自动跳转到登录页
}

// 获取用户信息
const userInfo = app.getUserInfo()

// 检查是否为游客用户
const isGuest = app.isGuestUser()
```

## 后端集成（可选）

如果需要集成后端服务，可以：

1. 修改 `utils/api.js` 中的 `BASE_URL`
2. 在 `utils/wechat-auth.js` 的 `login()` 方法中启用后端API调用
3. 实现后端接口来处理微信登录code并返回openid等信息

### 后端接口示例

```javascript
// 在 wechat-auth.js 中启用后端登录
const { wechatAPI } = require('./api.js')

// 在 login() 方法中添加：
const serverResponse = await wechatAPI.login(code, userInfo)
```

## 注意事项

1. **微信小程序要求**: 使用微信登录需要在微信公众平台配置域名白名单
2. **用户信息获取**: 新版本微信小程序推荐使用 `getUserProfile` 接口
3. **登录有效期**: 微信登录session有效期约为30天
4. **手机号登录**: 需要企业认证才能使用手机号登录功能
5. **数据同步**: 游客模式下的数据无法同步到云端

## 错误处理

常见错误及解决方法：

- `用户拒绝授权`: 提示用户授权的重要性
- `网络连接失败`: 检查网络状态和服务器连接
- `登录凭证失败`: 重新获取微信登录code
- `会话过期`: 引导用户重新登录

## 安全建议

1. 不要在前端存储敏感信息（如session_key）
2. 定期检查登录状态的有效性
3. 对用户数据进行加密存储
4. 实现合理的登录过期机制
