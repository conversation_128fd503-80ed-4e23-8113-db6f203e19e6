{"compileType": "miniprogram", "libVersion": "2.19.4", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false, "useCompilerPlugins": false}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wxb5a3130c96b59cc7", "simulatorPluginLibVersion": {}}