/**
 * 通用工具函数模块
 * 整合小程序中重复使用的工具函数
 */

/**
 * 时间格式化相关函数
 */

// 格式化时间为 HH:MM 格式
const formatTime = (dateString) => {
  const date = new Date(dateString)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// 格式化日期为 YYYY年MM月DD日 格式
const formatDate = (dateString) => {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}年${month}月${day}日`
}

// 格式化日期时间为 MM-DD HH:MM 格式
const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}

// 格式化日期为表单输入格式 YYYY-MM-DD
const formatDateForInput = (dateString) => {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 格式化时间为表单输入格式 HH:MM
const formatTimeForInput = (dateString) => {
  const date = new Date(dateString)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// 格式化完整的日期文本，包含星期
const formatDateText = (date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  const weekday = weekdays[date.getDay()]
  
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  
  if (date.toDateString() === today.toDateString()) {
    return `今天 ${month}月${day}日`
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return `明天 ${month}月${day}日`
  } else {
    return `${month}月${day}日 星期${weekday}`
  }
}

// 格式化相对日期（用于备忘录等）
const formatRelativeDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = now - date
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `今天 ${hours}:${minutes}`
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${month}-${day}`
  }
}

// 格式化任务截止日期（相对时间）
const formatDueDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  const diffTime = taskDate - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return `今天 ${formatTime(dateString)}`
  } else if (diffDays === 1) {
    return `明天 ${formatTime(dateString)}`
  } else if (diffDays === -1) {
    return `昨天 ${formatTime(dateString)}`
  } else if (diffDays > 0) {
    return `${diffDays}天后 ${formatTime(dateString)}`
  } else {
    return `${Math.abs(diffDays)}天前 ${formatTime(dateString)}`
  }
}

/**
 * 状态转换相关函数
 */

// 获取优先级文本
const getPriorityText = (priority) => {
  const map = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return map[priority] || '普通'
}

// 获取简短优先级文本
const getPriorityTextShort = (priority) => {
  const map = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return map[priority] || '普通'
}

// 获取任务状态文本
const getStatusText = (status) => {
  const map = {
    pending: '待完成',
    completed: '已完成',
    overdue: '已逾期'
  }
  return map[status] || '未知'
}

// 获取日程分类文本
const getCategoryText = (category) => {
  const map = {
    work: '工作',
    personal: '个人',
    meeting: '会议',
    other: '其他'
  }
  return map[category] || '其他'
}

/**
 * 业务逻辑相关函数
 */

// 判断任务是否逾期
const isTaskOverdue = (task) => {
  if (task.status === 'completed') return false
  return new Date(task.dueDate) < new Date()
}

// 获取文本预览
const getTextPreview = (content, maxLength = 100) => {
  if (!content) return '暂无内容'
  return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString()
}

// 验证必填字段
const validateRequired = (value, fieldName) => {
  if (!value || !value.trim()) {
    wx.showToast({
      title: `请输入${fieldName}`,
      icon: 'none'
    })
    return false
  }
  return true
}

// 显示确认删除对话框
const showDeleteConfirm = (itemType = '项目') => {
  return new Promise((resolve) => {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除这个${itemType}吗？`,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 显示成功提示
const showSuccess = (message) => {
  wx.showToast({
    title: message,
    icon: 'success'
  })
}

// 显示错误提示
const showError = (message) => {
  wx.showToast({
    title: message,
    icon: 'none'
  })
}

// 获取微信用户信息
const getWechatUserInfo = () => {
  return new Promise((resolve, reject) => {
    wx.getUserProfile({
      desc: '用于完善用户资料显示',
      success: (res) => {
        const userInfo = res.userInfo
        
        // 检查是否为模拟数据
        const isSimulatedData = (
          userInfo.nickName === '微信用户' && 
          userInfo.avatarUrl && 
          userInfo.avatarUrl.includes('default')
        )
        
        if (isSimulatedData) {
          console.log('检测到模拟数据，在开发环境中使用默认用户信息')
          // 在开发环境中，提供更友好的提示
          wx.showModal({
            title: '开发环境提示',
            content: '当前为开发环境，无法获取真实微信用户信息。\n\n您可以在个人中心手动设置头像和昵称。',
            showCancel: true,
            cancelText: '知道了',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.switchTab({
                  url: '/pages/profile/profile'
                })
              }
            }
          })
          
          // 使用默认信息但标记为开发环境
          const defaultInfo = {
            nickName: '开发用户',
            avatarUrl: '/images/default-avatar.svg',
            isDevelopment: true
          }
          
          const app = getApp()
          app.globalData.userInfo = defaultInfo
          wx.setStorageSync('userInfo', defaultInfo)
          
          resolve(defaultInfo)
        } else {
          // 真实微信数据
          const app = getApp()
          app.globalData.userInfo = userInfo
          wx.setStorageSync('userInfo', userInfo)
          
          console.log('获取真实微信用户信息成功:', userInfo)
          resolve(userInfo)
        }
      },
      fail: (err) => {
        console.error('获取微信用户信息失败:', err)
        reject(err)
      }
    })
  })
}

// 检查并提示用户授权
const checkUserInfoAuth = () => {
  return new Promise((resolve) => {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取用户信息
          wx.getUserInfo({
            success: (userRes) => {
              const app = getApp()
              app.globalData.userInfo = userRes.userInfo
              wx.setStorageSync('userInfo', userRes.userInfo)
              resolve(userRes.userInfo)
            },
            fail: () => {
              resolve(null)
            }
          })
        } else {
          resolve(null)
        }
      },
      fail: () => {
        resolve(null)
      }
    })
  })
}

module.exports = {
  // 时间格式化
  formatTime,
  formatDate,
  formatDateTime,
  formatDateForInput,
  formatTimeForInput,
  formatDateText,
  formatRelativeDate,
  formatDueDate,
  
  // 状态转换
  getPriorityText,
  getPriorityTextShort,
  getStatusText,
  getCategoryText,
  
  // 业务逻辑
  isTaskOverdue,
  getTextPreview,
  generateId,
  validateRequired,
  showDeleteConfirm,
  showSuccess,
  showError,
  
  // 微信用户信息
  getWechatUserInfo,
  checkUserInfoAuth
}
