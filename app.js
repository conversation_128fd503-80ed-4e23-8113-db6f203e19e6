// 导入微信授权工具
const { wechatAuth } = require('./utils/wechat-auth.js')

App({
  onLaunch() {
    // 初始化本地存储
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 初始化任务数据
    if (!wx.getStorageSync('tasks')) {
      wx.setStorageSync('tasks', [])
    }
    if (!wx.getStorageSync('schedules')) {
      wx.setStorageSync('schedules', [])
    }
    if (!wx.getStorageSync('notes')) {
      wx.setStorageSync('notes', [])
    }

    // 检查用户登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  async checkLoginStatus() {
    try {
      // 使用微信登录工具验证登录状态
      const isLoginValid = await wechatAuth.validateLogin()
      
      if (isLoginValid) {
        const userInfo = wx.getStorageSync('userInfo')
        const loginTime = wx.getStorageSync('loginTime')
        
        this.globalData.userInfo = userInfo
        this.globalData.isLoggedIn = true
        this.globalData.loginTime = loginTime
        
        console.log('用户已登录，登录时间:', new Date(loginTime))
      } else {
        console.log('登录状态无效，需要重新登录')
        this.globalData.isLoggedIn = false
        wechatAuth.clearLoginInfo()
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
      this.globalData.isLoggedIn = false
    }
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      // 先获取登录凭证
      wx.login({
        success: (loginRes) => {
          if (loginRes.code) {
            // 设置登录状态
            this.globalData.isLoggedIn = true
            
            // 尝试获取微信用户信息
            this.tryGetUserInfo().then((userInfo) => {
              resolve(userInfo)
            }).catch(() => {
              // 如果获取失败，使用默认信息
              const defaultUserInfo = {
                nickName: '智能用户' + Math.floor(Math.random() * 999 + 1),
                avatarUrl: '/images/default-avatar.svg'
              }
              wx.setStorageSync('userInfo', defaultUserInfo)
              this.globalData.userInfo = defaultUserInfo
              resolve(defaultUserInfo)
            })
          } else {
            reject(new Error('获取登录凭证失败'))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },
  
  // 尝试获取微信用户信息
  tryGetUserInfo() {
    return new Promise((resolve, reject) => {
      // 检查是否已经有用户信息
      const existingUserInfo = wx.getStorageSync('userInfo')
      if (existingUserInfo && existingUserInfo.nickName && existingUserInfo.nickName !== '微信用户') {
        this.globalData.userInfo = existingUserInfo
        resolve(existingUserInfo)
        return
      }
      
      // 尝试静默获取用户信息（这个方法在新版本中可能不可用）
      wx.getUserInfo({
        success: (res) => {
          const userInfo = res.userInfo
          wx.setStorageSync('userInfo', userInfo)
          this.globalData.userInfo = userInfo
          resolve(userInfo)
        },
        fail: () => {
          reject(new Error('获取用户信息失败'))
        }
      })
    })
  },

  // 用户登出
  logout() {
    // 使用微信登录工具清除登录信息
    wechatAuth.clearLoginInfo()
    
    console.log('用户已登出')
    
    // 跳转到登录页面
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 检查是否需要登录
  requireLogin() {
    if (!this.globalData.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 1500
      })
      
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
      return false
    }
    return true
  },

  // 检查微信登录状态是否有效
  checkWechatLoginStatus() {
    return wechatAuth.checkSession()
  },

  // 获取用户信息（统一入口）
  getUserInfo() {
    return this.globalData.userInfo || wx.getStorageSync('userInfo') || null
  },

  // 检查是否为游客用户
  isGuestUser() {
    const userInfo = this.getUserInfo()
    return userInfo && userInfo.isGuest === true
  },
  
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    loginTime: null
  }
})