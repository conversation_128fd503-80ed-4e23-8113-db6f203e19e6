// pages/profile/profile.js
const app = getApp()
const { showDeleteConfirm, showSuccess, getWechatUserInfo } = require('../../utils/common')

Page({
  data: {
    userInfo: {},
    stats: {
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      overdueTasks: 0,
      totalNotes: 0,
      totalSchedules: 0
    },
    appVersion: '1.0.0'
  },

  onLoad() {
    this.getUserInfo()
    this.loadStats()
  },

  onShow() {
    this.getUserInfo()
    this.loadStats()
  },

  getUserInfo() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo') || {
      nickName: '微信用户',
      avatarUrl: '/images/default-avatar.svg'
    }
    
    this.setData({
      userInfo: userInfo
    })
  },

  async getUserProfile() {
    try {
      const userInfo = await getWechatUserInfo()
      
      // 更新页面数据
      this.setData({
        userInfo: userInfo
      })
      
      // 如果是开发环境的模拟数据，不显示成功提示（已在common.js中处理）
      if (!userInfo.isDevelopment) {
        showSuccess('微信用户信息获取成功')
      }
      
    } catch (err) {
      console.error('获取微信用户信息失败:', err)
      
      // 如果用户拒绝授权，提供手动设置选项
      wx.showModal({
        title: '授权提示',
        content: '需要获取您的微信头像和昵称用于完善资料显示。\n\n请重新点击并同意授权，或选择手动设置。',
        confirmText: '手动设置',
        cancelText: '取消',
        success: (modalRes) => {
          if (modalRes.confirm) {
            wx.navigateTo({
              url: '/pages/user-info/user-info'
            })
          }
        }
      })
    }
  },
  
  // 快速编辑昵称
  editNickname() {
    const that = this
    wx.showModal({
      title: '修改昵称',
      editable: true,
      placeholderText: '请输入新昵称',
      content: this.data.userInfo.nickName,
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          const userInfo = {
            ...that.data.userInfo,
            nickName: res.content.trim()
          }
          
          that.setData({
            userInfo: userInfo
          })
          
          app.globalData.userInfo = userInfo
          wx.setStorageSync('userInfo', userInfo)
          
          showSuccess('昵称修改成功')
        }
      }
    })
  },

  loadStats() {
    const tasks = wx.getStorageSync('tasks') || []
    const notes = wx.getStorageSync('notes') || []
    const schedules = wx.getStorageSync('schedules') || []
    const now = new Date()
    
    const stats = {
      totalTasks: tasks.length,
      completedTasks: tasks.filter(task => task.status === 'completed').length,
      pendingTasks: tasks.filter(task => task.status === 'pending').length,
      overdueTasks: tasks.filter(task => {
        if (task.status === 'completed') return false
        return new Date(task.dueDate) < now
      }).length,
      totalNotes: notes.length,
      totalSchedules: schedules.length
    }
    
    this.setData({ stats })
  },

  // 清除所有数据
  async clearAllData() {
    const confirmed = await showDeleteConfirm('所有数据')
    if (confirmed) {
      wx.clearStorageSync()
      
      // 重新设置用户信息
      const defaultUserInfo = {
        nickName: '微信用户',
        avatarUrl: '/images/default-avatar.svg'
      }
      app.globalData.userInfo = defaultUserInfo
      wx.setStorageSync('userInfo', defaultUserInfo)
      
      this.setData({
        userInfo: defaultUserInfo,
        stats: {
          totalTasks: 0,
          completedTasks: 0,
          pendingTasks: 0,
          overdueTasks: 0,
          totalNotes: 0,
          totalSchedules: 0
        }
      })
      
      showSuccess('数据已清除')
    }
  },

  // 关于应用
  showAbout() {
    const { appVersion } = this.data
    wx.showModal({
      title: '关于应用',
      content: `个人事务管理小程序\n\n版本: v${appVersion}\n\n功能特点：\n• 任务管理：创建、编辑、完成任务\n• 日程安排：管理日常日程\n• 备忘录：记录重要信息\n• 数据统计：直观的数据展示`,
      showCancel: false,
      confirmText: '知道了'
    })
  },
  
  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout()
        }
      }
    })
  }
})