// schedule.js
const app = getApp()
const { formatDateText, formatTime, getCategoryText, showDeleteConfirm, showSuccess } = require('../../utils/common')

Page({
  data: {
    selectedDate: '',
    selectedDateText: '',
    schedules: []
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return
    }
    
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]
    
    this.setData({
      selectedDate: todayStr,
      selectedDateText: formatDateText(today)
    })
    
    this.loadSchedules()
  },

  onShow() {
    this.loadSchedules()
  },

  bindDateChange(e) {
    const dateStr = e.detail.value
    const date = new Date(dateStr)
    
    this.setData({
      selectedDate: dateStr,
      selectedDateText: formatDateText(date)
    })
    
    this.loadSchedules()
  },

  loadSchedules() {
    const schedules = wx.getStorageSync('schedules') || []
    const selectedDate = this.data.selectedDate
    
    const daySchedules = schedules
      .filter(schedule => {
        const scheduleDate = new Date(schedule.datetime).toISOString().split('T')[0]
        return scheduleDate === selectedDate
      })
      .sort((a, b) => new Date(a.datetime) - new Date(b.datetime))
      .map(schedule => ({
        ...schedule,
        timeText: formatTime(schedule.datetime),
        categoryText: getCategoryText(schedule.category)
      }))
    
    this.setData({ schedules: daySchedules })
  },


  editSchedule(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/add-schedule/add-schedule?id=${id}`
    })
  },

  async deleteSchedule(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    
    const confirmed = await showDeleteConfirm('日程')
    if (confirmed) {
      const schedules = wx.getStorageSync('schedules') || []
      const filteredSchedules = schedules.filter(schedule => schedule.id !== id)
      wx.setStorageSync('schedules', filteredSchedules)
      this.loadSchedules()
      
      showSuccess('日程已删除')
    }
  },

  goToAddSchedule() {
    wx.navigateTo({
      url: `/pages/add-schedule/add-schedule?date=${this.data.selectedDate}`
    })
  },

  goToScheduleDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/schedule-detail/schedule-detail?id=${id}`
    })
  }
})