// login-manager.js - 登录状态管理工具

/**
 * 登录管理器
 * 统一管理登录状态、用户信息和登录凭证
 */
class LoginManager {
  constructor() {
    this.loginInfo = {
      code: '',
      openid: '',
      sessionKey: '',
      environment: '',
      timestamp: 0
    }
  }

  /**
   * 获取当前环境类型
   */
  getEnvironmentType() {
    try {
      const accountInfo = wx.getAccountInfoSync()
      const envVersion = accountInfo.miniProgram.envVersion
      
      switch (envVersion) {
        case 'develop':
          return '开发环境'
        case 'trial':
          return '体验版'
        case 'release':
          return '正式版'
        default:
          return '未知环境'
      }
    } catch (error) {
      console.error('获取环境信息失败:', error)
      return '未知环境'
    }
  }

  /**
   * 是否为开发环境
   */
  isDevelopment() {
    try {
      const accountInfo = wx.getAccountInfoSync()
      return accountInfo.miniProgram.envVersion === 'develop' || 
             accountInfo.miniProgram.envVersion === 'trial'
    } catch (error) {
      return false
    }
  }

  /**
   * 生成模拟登录信息
   */
  generateMockLoginInfo(code) {
    const timestamp = Date.now()
    return {
      code: code,
      openid: `mock_openid_${timestamp}`,
      sessionKey: `mock_session_key_${timestamp}`,
      environment: this.getEnvironmentType(),
      timestamp: timestamp,
      isMock: true
    }
  }

  /**
   * 保存登录信息
   */
  saveLoginInfo(loginInfo) {
    this.loginInfo = {
      ...loginInfo,
      environment: this.getEnvironmentType(),
      timestamp: Date.now()
    }
    
    // 保存到本地存储
    wx.setStorageSync('loginInfo', this.loginInfo)
    
    console.log('登录信息已保存:', {
      ...this.loginInfo,
      sessionKey: this.loginInfo.sessionKey ? '***' : ''
    })
  }

  /**
   * 获取登录信息
   */
  getLoginInfo() {
    if (!this.loginInfo.code) {
      // 尝试从本地存储恢复
      const stored = wx.getStorageSync('loginInfo')
      if (stored) {
        this.loginInfo = stored
      }
    }
    return this.loginInfo
  }

  /**
   * 清除登录信息
   */
  clearLoginInfo() {
    this.loginInfo = {
      code: '',
      openid: '',
      sessionKey: '',
      environment: '',
      timestamp: 0
    }
    wx.removeStorageSync('loginInfo')
    console.log('登录信息已清除')
  }

  /**
   * 检查登录信息是否有效
   */
  isLoginInfoValid() {
    const info = this.getLoginInfo()
    
    // 检查基本信息是否存在
    if (!info.code || !info.openid) {
      return false
    }
    
    // 检查是否过期（24小时）
    const now = Date.now()
    const expireTime = 24 * 60 * 60 * 1000 // 24小时
    
    if (info.timestamp && (now - info.timestamp) > expireTime) {
      console.log('登录信息已过期')
      return false
    }
    
    return true
  }

  /**
   * 格式化登录信息用于显示
   */
  formatLoginInfoForDisplay() {
    const info = this.getLoginInfo()
    const app = getApp()
    const userInfo = app.globalData.userInfo || {}
    
    return {
      environment: info.environment || this.getEnvironmentType(),
      userName: userInfo.nickName || '未知用户',
      code: info.code || '未获取',
      openid: info.openid || '未获取',
      sessionKey: info.sessionKey ? `${info.sessionKey.substring(0, 10)}...` : '未获取',
      loginTime: info.timestamp ? new Date(info.timestamp).toLocaleString() : '未知',
      isMock: info.isMock || false,
      isValid: this.isLoginInfoValid()
    }
  }

  /**
   * 获取用于复制的完整信息
   */
  getFullInfoForCopy() {
    const displayInfo = this.formatLoginInfoForDisplay()
    
    return `=== 登录状态信息 ===
环境: ${displayInfo.environment}
用户: ${displayInfo.userName}
登录时间: ${displayInfo.loginTime}
状态: ${displayInfo.isValid ? '有效' : '无效'}
类型: ${displayInfo.isMock ? '模拟数据' : '真实数据'}

=== 技术信息 ===
Code: ${displayInfo.code}
OpenID: ${displayInfo.openid}
SessionKey: ${displayInfo.sessionKey}

=== 说明 ===
${displayInfo.isMock ? '当前使用模拟登录数据，适用于开发和测试。' : '当前使用真实登录数据。'}
${displayInfo.isValid ? '登录状态有效。' : '登录状态已过期，建议重新登录。'}`
  }

  /**
   * 显示登录信息弹窗
   */
  showLoginInfoModal() {
    const displayInfo = this.formatLoginInfoForDisplay()
    
    const content = `环境: ${displayInfo.environment}
用户: ${displayInfo.userName}
状态: ${displayInfo.isValid ? '✅ 有效' : '❌ 无效'}
类型: ${displayInfo.isMock ? '🔧 模拟' : '✅ 真实'}
时间: ${displayInfo.loginTime}

Code: ${displayInfo.code}
OpenID: ${displayInfo.openid}
SessionKey: ${displayInfo.sessionKey}`
    
    wx.showModal({
      title: '登录状态信息',
      content: content,
      showCancel: true,
      cancelText: '复制详情',
      confirmText: '确定',
      success: (res) => {
        if (res.cancel) {
          // 复制完整信息到剪贴板
          wx.setClipboardData({
            data: this.getFullInfoForCopy(),
            success: () => {
              wx.showToast({
                title: '详细信息已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  }
}

// 导出单例
const loginManager = new LoginManager()

module.exports = {
  loginManager,
  LoginManager
}
