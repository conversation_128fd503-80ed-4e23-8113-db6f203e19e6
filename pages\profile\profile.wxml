<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="card user-card">
    <view class="user-info" bindtap="getUserProfile">
      <view class="avatar-container">
        <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.svg'}}" mode="aspectFill"></image>
        <text class="update-hint">点击获取微信信息</text>
      </view>
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName || '微信用户'}}</text>
        <text class="user-desc">个人事务管理系统</text>
      </view>
    </view>
  </view>

  <!-- 数据统计卡片 -->
  <view class="card">
    <view class="card-header">
      <text class="card-title">数据统计</text>
    </view>
    <view class="stats-grid">
      <view class="stat-card">
        <view class="stat-number text-primary">{{stats.totalTasks}}</view>
        <view class="stat-label">总任务</view>
      </view>
      <view class="stat-card">
        <view class="stat-number text-success">{{stats.completedTasks}}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-card">
        <view class="stat-number text-warning">{{stats.pendingTasks}}</view>
        <view class="stat-label">待完成</view>
      </view>
      <view class="stat-card">
        <view class="stat-number text-danger">{{stats.overdueTasks}}</view>
        <view class="stat-label">已逾期</view>
      </view>
    </view>
    
    <!-- 第二行统计 -->
    <view class="stats-grid stats-secondary">
      <view class="stat-card">
        <view class="stat-number text-info">{{stats.totalNotes}}</view>
        <view class="stat-label">备忘录</view>
      </view>
      <view class="stat-card">
        <view class="stat-number text-purple">{{stats.totalSchedules}}</view>
        <view class="stat-label">日程数</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单卡片 -->
  <view class="card">
    <view class="card-header">
      <text class="card-title">设置</text>
    </view>
    <view class="menu-list">
      <view class="menu-item" bindtap="editNickname">
        <view class="menu-content">
          <view class="menu-icon">✏️</view>
          <view class="menu-info">
            <text class="menu-title">修改昵称</text>
            <text class="menu-desc">快速修改显示昵称</text>
          </view>
        </view>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="showAbout">
        <view class="menu-content">
          <view class="menu-icon">📱</view>
          <view class="menu-info">
            <text class="menu-title">关于应用</text>
            <text class="menu-desc">查看应用信息和功能特点</text>
          </view>
        </view>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="logout">
        <view class="menu-content">
          <view class="menu-icon">🚪</view>
          <view class="menu-info">
            <text class="menu-title">退出登录</text>
            <text class="menu-desc">退出当前账户</text>
          </view>
        </view>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item danger-item" bindtap="clearAllData">
        <view class="menu-content">
          <view class="menu-icon">🗑️</view>
          <view class="menu-info">
            <text class="menu-title danger">清除所有数据</text>
            <text class="menu-desc">删除所有任务、日程和备忘录</text>
          </view>
        </view>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>
</view>
