# 工具函数使用说明

## 概述
`utils/common.js` 模块提供了小程序中常用的工具函数，用于统一处理时间格式化、状态转换、业务逻辑等功能。

## 使用方法

### 引入模块
```javascript
// 引入所需的工具函数
const { formatTime, formatDate, getPriorityText } = require('../../utils/common')

// 或者引入全部函数
const utils = require('../../utils/common')
```

## 函数分类

### 时间格式化函数

#### formatTime(dateString)
格式化时间为 HH:MM 格式
```javascript
formatTime('2023-12-25T14:30:00.000Z') // "14:30"
```

#### formatDate(dateString)
格式化日期为 YYYY年MM月DD日 格式
```javascript
formatDate('2023-12-25T14:30:00.000Z') // "2023年12月25日"
```

#### formatDateTime(dateString)
格式化日期时间为 MM-DD HH:MM 格式
```javascript
formatDateTime('2023-12-25T14:30:00.000Z') // "12-25 14:30"
```

#### formatDateForInput(dateString)
格式化日期为表单输入格式 YYYY-MM-DD
```javascript
formatDateForInput('2023-12-25T14:30:00.000Z') // "2023-12-25"
```

#### formatTimeForInput(dateString)
格式化时间为表单输入格式 HH:MM
```javascript
formatTimeForInput('2023-12-25T14:30:00.000Z') // "14:30"
```

#### formatDateText(date)
格式化完整的日期文本，包含星期和相对时间
```javascript
formatDateText(new Date()) // "今天 12月25日" 或 "12月25日 星期一"
```

#### formatRelativeDate(dateString)
格式化相对日期（用于备忘录等）
```javascript
formatRelativeDate('2023-12-24T14:30:00.000Z') // "昨天" 或 "2天前"
```

#### formatDueDate(dateString)
格式化任务截止日期（相对时间）
```javascript
formatDueDate('2023-12-25T14:30:00.000Z') // "今天 14:30" 或 "明天 14:30"
```

### 状态转换函数

#### getPriorityText(priority)
获取优先级文本
```javascript
getPriorityText('high') // "高优先级"
getPriorityText('medium') // "中优先级"
getPriorityText('low') // "低优先级"
```

#### getPriorityTextShort(priority)
获取简短优先级文本
```javascript
getPriorityTextShort('high') // "高"
```

#### getStatusText(status)
获取任务状态文本
```javascript
getStatusText('pending') // "待完成"
getStatusText('completed') // "已完成"
getStatusText('overdue') // "已逾期"
```

#### getCategoryText(category)
获取日程分类文本
```javascript
getCategoryText('work') // "工作"
getCategoryText('meeting') // "会议"
```

### 业务逻辑函数

#### isTaskOverdue(task)
判断任务是否逾期
```javascript
isTaskOverdue({ status: 'pending', dueDate: '2023-12-20T14:30:00.000Z' }) // true/false
```

#### getTextPreview(content, maxLength)
获取文本预览
```javascript
getTextPreview('这是一段很长的文本内容...', 50) // "这是一段很长的文本内容..."
```

#### generateId()
生成唯一ID
```javascript
generateId() // "1703512800000"
```

#### validateRequired(value, fieldName)
验证必填字段
```javascript
validateRequired(title, '任务标题') // 返回 true/false，失败时显示错误提示
```

### UI交互函数

#### showDeleteConfirm(itemType)
显示确认删除对话框（返回Promise）
```javascript
const confirmed = await showDeleteConfirm('任务')
if (confirmed) {
  // 执行删除操作
}
```

#### showSuccess(message)
显示成功提示
```javascript
showSuccess('保存成功')
```

#### showError(message)
显示错误提示
```javascript
showError('请输入标题')
```

## 使用示例

### 在页面中使用工具函数
```javascript
// pages/example/example.js
const { formatDate, getPriorityText, showSuccess } = require('../../utils/common')

Page({
  data: {
    task: null
  },
  
  onLoad() {
    const task = {
      title: '示例任务',
      priority: 'high',
      dueDate: '2023-12-25T14:30:00.000Z'
    }
    
    this.setData({
      task: {
        ...task,
        priorityText: getPriorityText(task.priority),
        dueDateText: formatDate(task.dueDate)
      }
    })
  },
  
  saveTask() {
    // 保存逻辑...
    showSuccess('任务保存成功')
  }
})
```

## 迁移说明
重构完成后，各页面的重复工具函数已被移除，统一使用 `utils/common.js` 中的函数。这样做的好处：

1. **代码复用**：避免重复代码，减少维护成本
2. **一致性**：确保所有页面使用相同的格式化逻辑
3. **易维护**：修改格式化逻辑只需要在一个地方修改
4. **可测试性**：工具函数可以独立测试
