// api.js - 后端API接口
const BASE_URL = 'https://your-backend-domain.com/api' // 请替换为您的后端域名

/**
 * 网络请求封装
 */
function request(url, options = {}) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}${url}`,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}`))
        }
      },
      fail: (error) => {
        console.error('网络请求失败:', error)
        reject(new Error('网络连接失败'))
      }
    })
  })
}

/**
 * 微信登录相关API
 */
const wechatAPI = {
  /**
   * 微信登录 - 发送code到后端获取openid和session_key
   * @param {string} code - 微信登录code
   * @param {object} userInfo - 用户信息
   */
  login(code, userInfo) {
    return request('/wechat/login', {
      method: 'POST',
      data: {
        code,
        userInfo
      }
    })
  },

  /**
   * 解密手机号
   * @param {string} code - 微信登录code
   * @param {string} encryptedData - 加密数据
   * @param {string} iv - 加密初始向量
   */
  decryptPhone(code, encryptedData, iv) {
    return request('/wechat/decrypt-phone', {
      method: 'POST',
      data: {
        code,
        encryptedData,
        iv
      }
    })
  },

  /**
   * 刷新用户信息
   * @param {string} openid - 用户openid
   */
  refreshUserInfo(openid) {
    return request('/wechat/user-info', {
      method: 'GET',
      data: {
        openid
      }
    })
  }
}

/**
 * 用户相关API
 */
const userAPI = {
  /**
   * 获取用户详细信息
   * @param {string} userId - 用户ID
   */
  getUserDetail(userId) {
    return request(`/user/${userId}`, {
      method: 'GET'
    })
  },

  /**
   * 更新用户信息
   * @param {string} userId - 用户ID
   * @param {object} userInfo - 用户信息
   */
  updateUserInfo(userId, userInfo) {
    return request(`/user/${userId}`, {
      method: 'PUT',
      data: userInfo
    })
  }
}

/**
 * 数据同步API（用于同步任务、日程、备忘录等数据）
 */
const syncAPI = {
  /**
   * 同步用户数据到服务器
   * @param {string} userId - 用户ID
   * @param {object} data - 要同步的数据
   */
  syncToServer(userId, data) {
    return request('/sync/upload', {
      method: 'POST',
      data: {
        userId,
        ...data
      }
    })
  },

  /**
   * 从服务器获取用户数据
   * @param {string} userId - 用户ID
   */
  syncFromServer(userId) {
    return request(`/sync/download/${userId}`, {
      method: 'GET'
    })
  }
}

module.exports = {
  request,
  wechatAPI,
  userAPI,
  syncAPI
}
