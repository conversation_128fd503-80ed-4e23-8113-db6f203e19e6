// wechat-auth.js - 微信登录授权工具

/**
 * 微信登录授权管理器
 */
class WechatAuth {
  constructor() {
    this.isProcessing = false
  }

  // 获取app实例
  getApp() {
    try {
      return getApp()
    } catch (error) {
      console.error('获取app实例失败:', error)
      return null
    }
  }

  /**
   * 检查微信登录会话是否有效
   */
  checkSession() {
    return new Promise((resolve) => {
      wx.checkSession({
        success: () => {
          console.log('微信登录会话有效')
          resolve(true)
        },
        fail: () => {
          console.log('微信登录会话已过期')
          resolve(false)
        }
      })
    })
  }

  /**
   * 获取微信登录code
   */
  getLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('获取微信登录code成功:', res.code)
            resolve(res.code)
          } else {
            reject(new Error('获取登录凭证失败'))
          }
        },
        fail: (error) => {
          console.error('微信登录失败:', error)
          reject(new Error('微信登录失败'))
        }
      })
    })
  }

  /**
   * 获取用户授权设置
   */
  getSetting() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          resolve(res.authSetting)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  /**
   * 获取用户信息（新接口）
   */
  getUserProfile(desc = '用于完善用户资料') {
    return new Promise((resolve, reject) => {
      if (!wx.getUserProfile) {
        console.log('当前环境不支持getUserProfile接口')
        reject(new Error('当前版本不支持getUserProfile'))
        return
      }

      wx.getUserProfile({
        desc: desc,
        success: (res) => {
          console.log('getUserProfile成功:', res.userInfo)
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('getUserProfile失败:', error)

          // 详细的错误处理
          if (error.errMsg) {
            if (error.errMsg.includes('deny') || error.errMsg.includes('cancel')) {
              reject(new Error('用户拒绝授权'))
            } else if (error.errMsg.includes('system')) {
              reject(new Error('系统错误，请重试'))
            } else {
              reject(new Error(`获取用户信息失败: ${error.errMsg}`))
            }
          } else {
            reject(new Error('获取用户信息失败'))
          }
        }
      })
    })
  }

  /**
   * 获取用户信息（旧接口）
   */
  getUserInfo() {
    return new Promise((resolve, reject) => {
      wx.getUserInfo({
        success: (res) => {
          console.log('getUserInfo成功:', res.userInfo)
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('getUserInfo失败:', error)
          reject(new Error('获取用户信息失败'))
        }
      })
    })
  }

  /**
   * 智能获取用户信息
   * 根据当前环境和授权状态选择最合适的方式获取用户信息
   */
  async getSmartUserInfo() {
    console.log('开始智能获取用户信息...')

    try {
      // 1. 检查授权状态，如果已授权则优先使用旧接口（更稳定）
      const authSetting = await this.getSetting()
      if (authSetting['scope.userInfo']) {
        console.log('检测到已有授权，使用 getUserInfo 接口')
        try {
          return await this.getUserInfo()
        } catch (error) {
          console.log('getUserInfo 失败，尝试其他方式:', error.message)
        }
      }

      // 2. 尝试使用新的 getUserProfile 接口
      if (wx.getUserProfile) {
        console.log('尝试使用 getUserProfile 接口获取用户信息')
        try {
          return await this.getUserProfile('用于完善个人资料显示')
        } catch (error) {
          console.log('getUserProfile 失败:', error.message)

          // 如果是用户拒绝授权，直接抛出错误
          if (error.message.includes('拒绝授权')) {
            throw error
          }
        }
      }

      // 3. 如果都失败了，返回默认用户信息（开发环境友好）
      console.log('所有获取用户信息的方式都失败，使用默认用户信息')
      const defaultUserInfo = {
        nickName: '微信用户',
        avatarUrl: '/images/default-avatar.svg',
        gender: 0,
        country: '',
        province: '',
        city: '',
        language: 'zh_CN'
      }

      // 在开发环境中，我们可以使用默认信息
      console.log('返回默认用户信息:', defaultUserInfo)
      return defaultUserInfo

    } catch (error) {
      console.error('智能获取用户信息失败:', error)

      // 如果是用户拒绝授权，抛出特定错误
      if (error.message && error.message.includes('拒绝授权')) {
        throw new Error('用户拒绝授权')
      }

      // 对于其他错误，在开发环境中返回默认信息，生产环境抛出错误
      console.log('发生错误，但返回默认用户信息以便开发调试')
      return {
        nickName: '开发用户',
        avatarUrl: '/images/default-avatar.svg',
        gender: 0,
        country: '',
        province: '',
        city: '',
        language: 'zh_CN'
      }
    }
  }

  /**
   * 完整的微信授权登录流程
   */
  async login() {
    if (this.isProcessing) {
      throw new Error('登录正在进行中，请稍后')
    }

    this.isProcessing = true
    console.log('开始微信授权登录流程...')

    try {
      // 1. 检查当前微信会话状态
      const isSessionValid = await this.checkSession()
      console.log('微信会话状态:', isSessionValid)

      // 2. 获取微信登录code
      const code = await this.getLoginCode()
      console.log('获取登录code成功:', code)

      // 3. 获取用户信息（这里会触发授权弹窗）
      const userInfo = await this.getSmartUserInfo()
      console.log('获取用户信息成功:', userInfo.nickName)

      // 4. 构建完整的用户登录信息
      const loginTime = new Date().getTime()
      const completeUserInfo = {
        ...userInfo,
        loginTime: loginTime,
        loginType: 'wechat',
        loginCode: code,
        sessionValid: isSessionValid,
        // 预留后端集成字段
        // openid: serverResponse.openid,
        // unionid: serverResponse.unionid,
        // sessionKey: serverResponse.sessionKey
      }

      // 5. 保存登录信息到本地和全局状态
      this.saveLoginInfo(completeUserInfo)

      console.log('微信授权登录成功:', {
        nickName: completeUserInfo.nickName,
        loginType: completeUserInfo.loginType,
        loginTime: new Date(completeUserInfo.loginTime).toLocaleString()
      })

      return completeUserInfo

    } catch (error) {
      console.error('微信授权登录失败:', error)

      // 根据错误类型提供更详细的错误信息
      if (error.message.includes('拒绝授权')) {
        throw new Error('用户拒绝授权，无法完成登录')
      } else if (error.message.includes('网络')) {
        throw new Error('网络连接失败，请检查网络后重试')
      } else if (error.message.includes('登录凭证')) {
        throw new Error('获取登录凭证失败，请重试')
      }

      throw error
    } finally {
      this.isProcessing = false
      console.log('微信登录流程结束')
    }
  }

  /**
   * 保存登录信息
   */
  saveLoginInfo(userInfo) {
    const loginTime = userInfo.loginTime || new Date().getTime()
    const app = this.getApp()
    
    // 保存到全局数据
    if (app && app.globalData) {
      app.globalData.userInfo = userInfo
      app.globalData.isLoggedIn = true
      app.globalData.loginTime = loginTime
    }
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('isLoggedIn', true)
    wx.setStorageSync('loginTime', loginTime)
    
    console.log('用户信息已保存')
  }

  /**
   * 清除登录信息
   */
  clearLoginInfo() {
    const app = this.getApp()
    
    // 清除本地存储
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('isLoggedIn')
    wx.removeStorageSync('loginTime')
    
    // 清除全局数据
    if (app && app.globalData) {
      app.globalData.userInfo = null
      app.globalData.isLoggedIn = false
      app.globalData.loginTime = null
    }
    
    console.log('登录信息已清除')
  }

  /**
   * 验证登录状态
   */
  async validateLogin() {
    try {
      const app = this.getApp()
      const userInfo = (app && app.globalData && app.globalData.userInfo) || wx.getStorageSync('userInfo')
      const loginTime = (app && app.globalData && app.globalData.loginTime) || wx.getStorageSync('loginTime')
      
      if (!userInfo || !loginTime) {
        return false
      }

      // 检查登录是否过期
      const currentTime = new Date().getTime()
      const expireTime = 30 * 24 * 60 * 60 * 1000 // 30天
      
      if ((currentTime - loginTime) > expireTime) {
        console.log('登录已过期')
        this.clearLoginInfo()
        return false
      }

      // 检查微信session是否有效
      const isSessionValid = await this.checkSession()
      if (!isSessionValid && userInfo.loginType === 'wechat') {
        console.log('微信session已过期')
        return false
      }

      return true
      
    } catch (error) {
      console.error('验证登录状态失败:', error)
      return false
    }
  }
}

// 导出单例
const wechatAuth = new WechatAuth()

module.exports = {
  wechatAuth,
  WechatAuth
}
