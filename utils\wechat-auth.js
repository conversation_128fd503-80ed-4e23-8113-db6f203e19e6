// wechat-auth.js - 微信登录授权工具

/**
 * 微信登录授权管理器
 */
class WechatAuth {
  constructor() {
    this.isProcessing = false
  }

  // 获取app实例
  getApp() {
    try {
      return getApp()
    } catch (error) {
      console.error('获取app实例失败:', error)
      return null
    }
  }

  /**
   * 检查微信登录会话是否有效
   */
  checkSession() {
    return new Promise((resolve) => {
      wx.checkSession({
        success: () => {
          console.log('微信登录会话有效')
          resolve(true)
        },
        fail: () => {
          console.log('微信登录会话已过期')
          resolve(false)
        }
      })
    })
  }

  /**
   * 获取微信登录code
   */
  getLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('获取微信登录code成功:', res.code)
            resolve(res.code)
          } else {
            reject(new Error('获取登录凭证失败'))
          }
        },
        fail: (error) => {
          console.error('微信登录失败:', error)
          reject(new Error('微信登录失败'))
        }
      })
    })
  }

  /**
   * 获取用户授权设置
   */
  getSetting() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          resolve(res.authSetting)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  /**
   * 获取用户信息（新接口）
   */
  getUserProfile(desc = '用于完善用户资料') {
    return new Promise((resolve, reject) => {
      if (!wx.getUserProfile) {
        reject(new Error('当前版本不支持getUserProfile'))
        return
      }

      wx.getUserProfile({
        desc: desc,
        success: (res) => {
          console.log('getUserProfile成功:', res.userInfo)
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('getUserProfile失败:', error)
          if (error.errMsg && error.errMsg.includes('deny')) {
            reject(new Error('用户拒绝授权'))
          } else {
            reject(new Error('获取用户信息失败'))
          }
        }
      })
    })
  }

  /**
   * 获取用户信息（旧接口）
   */
  getUserInfo() {
    return new Promise((resolve, reject) => {
      wx.getUserInfo({
        success: (res) => {
          console.log('getUserInfo成功:', res.userInfo)
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('getUserInfo失败:', error)
          reject(new Error('获取用户信息失败'))
        }
      })
    })
  }

  /**
   * 智能获取用户信息
   * 根据当前环境和授权状态选择最合适的方式获取用户信息
   */
  async getSmartUserInfo() {
    try {
      console.log('开始智能获取用户信息...')

      // 1. 优先使用新的 getUserProfile 接口（推荐方式）
      if (wx.getUserProfile) {
        console.log('使用 getUserProfile 接口获取用户信息')
        return await this.getUserProfile('用于完善个人资料显示')
      }

      // 2. 检查授权状态，如果已授权则使用旧接口
      const authSetting = await this.getSetting()
      if (authSetting['scope.userInfo']) {
        console.log('检测到已有授权，使用 getUserInfo 接口')
        return await this.getUserInfo()
      }

      // 3. 如果都不支持或未授权，返回默认用户信息
      console.log('使用默认用户信息')
      return {
        nickName: '微信用户',
        avatarUrl: '/images/default-avatar.svg',
        gender: 0,
        country: '',
        province: '',
        city: '',
        language: 'zh_CN'
      }

    } catch (error) {
      console.error('智能获取用户信息失败:', error)

      // 如果是用户拒绝授权，抛出特定错误
      if (error.message && error.message.includes('拒绝授权')) {
        throw new Error('用户拒绝授权')
      }

      // 其他错误也重新抛出
      throw error
    }
  }

  /**
   * 完整的微信授权登录流程
   */
  async login() {
    if (this.isProcessing) {
      throw new Error('登录正在进行中，请稍后')
    }

    this.isProcessing = true
    console.log('开始微信授权登录流程...')

    try {
      // 1. 检查当前微信会话状态
      const isSessionValid = await this.checkSession()
      console.log('微信会话状态:', isSessionValid)

      // 2. 获取微信登录code
      const code = await this.getLoginCode()
      console.log('获取登录code成功:', code)

      // 3. 获取用户信息（这里会触发授权弹窗）
      const userInfo = await this.getSmartUserInfo()
      console.log('获取用户信息成功:', userInfo.nickName)

      // 4. 构建完整的用户登录信息
      const loginTime = new Date().getTime()
      const completeUserInfo = {
        ...userInfo,
        loginTime: loginTime,
        loginType: 'wechat',
        loginCode: code,
        sessionValid: isSessionValid,
        // 预留后端集成字段
        // openid: serverResponse.openid,
        // unionid: serverResponse.unionid,
        // sessionKey: serverResponse.sessionKey
      }

      // 5. 保存登录信息到本地和全局状态
      this.saveLoginInfo(completeUserInfo)

      console.log('微信授权登录成功:', {
        nickName: completeUserInfo.nickName,
        loginType: completeUserInfo.loginType,
        loginTime: new Date(completeUserInfo.loginTime).toLocaleString()
      })

      return completeUserInfo

    } catch (error) {
      console.error('微信授权登录失败:', error)

      // 根据错误类型提供更详细的错误信息
      if (error.message.includes('拒绝授权')) {
        throw new Error('用户拒绝授权，无法完成登录')
      } else if (error.message.includes('网络')) {
        throw new Error('网络连接失败，请检查网络后重试')
      } else if (error.message.includes('登录凭证')) {
        throw new Error('获取登录凭证失败，请重试')
      }

      throw error
    } finally {
      this.isProcessing = false
      console.log('微信登录流程结束')
    }
  }

  /**
   * 保存登录信息
   */
  saveLoginInfo(userInfo) {
    const loginTime = userInfo.loginTime || new Date().getTime()
    const app = this.getApp()
    
    // 保存到全局数据
    if (app && app.globalData) {
      app.globalData.userInfo = userInfo
      app.globalData.isLoggedIn = true
      app.globalData.loginTime = loginTime
    }
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('isLoggedIn', true)
    wx.setStorageSync('loginTime', loginTime)
    
    console.log('用户信息已保存')
  }

  /**
   * 清除登录信息
   */
  clearLoginInfo() {
    const app = this.getApp()
    
    // 清除本地存储
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('isLoggedIn')
    wx.removeStorageSync('loginTime')
    
    // 清除全局数据
    if (app && app.globalData) {
      app.globalData.userInfo = null
      app.globalData.isLoggedIn = false
      app.globalData.loginTime = null
    }
    
    console.log('登录信息已清除')
  }

  /**
   * 验证登录状态
   */
  async validateLogin() {
    try {
      const app = this.getApp()
      const userInfo = (app && app.globalData && app.globalData.userInfo) || wx.getStorageSync('userInfo')
      const loginTime = (app && app.globalData && app.globalData.loginTime) || wx.getStorageSync('loginTime')
      
      if (!userInfo || !loginTime) {
        return false
      }

      // 检查登录是否过期
      const currentTime = new Date().getTime()
      const expireTime = 30 * 24 * 60 * 60 * 1000 // 30天
      
      if ((currentTime - loginTime) > expireTime) {
        console.log('登录已过期')
        this.clearLoginInfo()
        return false
      }

      // 检查微信session是否有效
      const isSessionValid = await this.checkSession()
      if (!isSessionValid && userInfo.loginType === 'wechat') {
        console.log('微信session已过期')
        return false
      }

      return true
      
    } catch (error) {
      console.error('验证登录状态失败:', error)
      return false
    }
  }
}

// 导出单例
const wechatAuth = new WechatAuth()

module.exports = {
  wechatAuth,
  WechatAuth
}
