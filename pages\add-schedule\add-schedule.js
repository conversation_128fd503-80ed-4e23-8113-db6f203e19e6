// add-schedule.js
const { generateId, validateRequired, showSuccess, showError } = require('../../utils/common')

Page({
  data: {
    isEdit: false,
    scheduleId: null,
    today: '',
    formData: {
      title: '',
      description: '',
      date: '',
      time: '09:00',
      location: '',
      category: 'other',
      reminder: 'none'
    }
  },

  onLoad(options) {
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]
    
    this.setData({ today: todayStr })

    if (options.id) {
      this.setData({ 
        isEdit: true, 
        scheduleId: options.id 
      })
      this.loadSchedule(options.id)
    } else {
      // 设置默认日期
      const defaultDate = options.date || todayStr
      this.setData({
        'formData.date': defaultDate
      })
    }

    wx.setNavigationBarTitle({
      title: options.id ? '编辑日程' : '添加日程'
    })
  },

  loadSchedule(id) {
    const schedules = wx.getStorageSync('schedules') || []
    const schedule = schedules.find(s => s.id === id)
    
    if (schedule) {
      const datetime = new Date(schedule.datetime)
      const dateStr = datetime.toISOString().split('T')[0]
      const timeStr = datetime.toTimeString().slice(0, 5)
      
      this.setData({
        formData: {
          title: schedule.title,
          description: schedule.description || '',
          date: dateStr,
          time: timeStr,
          location: schedule.location || '',
          category: schedule.category,
          reminder: schedule.reminder || 'none'
        }
      })
    }
  },

  bindDateChange(e) {
    this.setData({
      'formData.date': e.detail.value
    })
  },

  bindTimeChange(e) {
    this.setData({
      'formData.time': e.detail.value
    })
  },

  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      'formData.category': category
    })
  },

  selectReminder(e) {
    const reminder = e.currentTarget.dataset.reminder
    this.setData({
      'formData.reminder': reminder
    })
  },

  submitSchedule(e) {
    const formData = e.detail.value
    const { date, time, category, reminder } = this.data.formData

    // 验证必填字段
    if (!validateRequired(formData.title, '日程标题')) {
      return
    }

    if (!date) {
      showError('请选择日期')
      return
    }

    if (!time) {
      showError('请选择时间')
      return
    }

    // 构建日程对象
    const datetime = new Date(`${date} ${time}`)
    const schedule = {
      id: this.data.isEdit ? this.data.scheduleId : generateId(),
      title: formData.title.trim(),
      description: formData.description.trim(),
      datetime: datetime.toISOString(),
      location: formData.location.trim(),
      category,
      reminder,
      createdAt: this.data.isEdit ? undefined : new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // 保存日程
    const schedules = wx.getStorageSync('schedules') || []
    
    if (this.data.isEdit) {
      const index = schedules.findIndex(s => s.id === this.data.scheduleId)
      if (index !== -1) {
        schedules[index] = { ...schedules[index], ...schedule }
      }
    } else {
      schedule.createdAt = new Date().toISOString()
      schedules.push(schedule)
    }

    wx.setStorageSync('schedules', schedules)

    showSuccess(this.data.isEdit ? '日程已更新' : '日程已添加')

    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  },

  goBack() {
    wx.navigateBack()
  }
})