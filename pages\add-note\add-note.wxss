/* add-note.wxss */
.tag-input-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
  margin-top: 16rpx;
}

.tag-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fafafa;
}

.tag-add-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: #e8f5e8;
  color: #4CAF50;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  gap: 8rpx;
}

.tag-remove {
  font-size: 32rpx;
  font-weight: bold;
  cursor: pointer;
  opacity: 0.7;
}

.form-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
}

.form-actions .btn {
  flex: 1;
}

.content-textarea {
  min-height: 400rpx;
}