/* add-schedule.wxss */
.category-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-top: 16rpx;
}

.category-option {
  text-align: center;
  padding: 20rpx 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fafafa;
  transition: all 0.3s;
}

.category-option.active {
  color: white;
  font-weight: 500;
}

.category-option.active.work {
  background-color: #1976d2;
  border-color: #1976d2;
}

.category-option.active.personal {
  background-color: #7b1fa2;
  border-color: #7b1fa2;
}

.category-option.active.meeting {
  background-color: #f57c00;
  border-color: #f57c00;
}

.category-option.active.other {
  background-color: #4caf50;
  border-color: #4caf50;
}

.reminder-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-top: 16rpx;
}

.reminder-option {
  text-align: center;
  padding: 20rpx 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fafafa;
  transition: all 0.3s;
}

.reminder-option.active {
  background-color: #4CAF50;
  border-color: #4CAF50;
  color: white;
  font-weight: 500;
}

.form-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
}

.form-actions .btn {
  flex: 1;
}

.picker {
  color: #333;
}

.picker:empty::before {
  content: attr(placeholder);
  color: #999;
}