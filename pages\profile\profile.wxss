/* profile.wxss */

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.user-info {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.update-hint {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.nickname {
  font-size: 40rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 0;
}

.stats-secondary {
  margin-top: 16rpx;
}

.stat-card {
  text-align: center;
  padding: 32rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

/* 额外的颜色类 */
.text-info {
  color: #17a2b8;
}

.text-purple {
  color: #6f42c1;
}

/* 菜单列表 */
.menu-list {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: rgba(0,0,0,0.05);
}

.menu-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.menu-info {
  flex: 1;
}

.menu-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.menu-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
  font-weight: 300;
}

/* 危险操作样式 */
.danger-item {
  background-color: rgba(244, 67, 54, 0.02);
}

.danger {
  color: #f44336 !important;
}

.danger-item .menu-icon {
  filter: grayscale(0.3);
}
