/**app.wxss**/
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 通用按钮样式 */
.btn {
  border-radius: 12rpx;
  font-size: 32rpx;
  padding: 24rpx 48rpx;
  text-align: center;
  border: none;
  transition: all 0.3s;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  margin-bottom: 24rpx;
  padding: 32rpx;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.card-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #4CAF50;
  background-color: white;
}

.form-textarea {
  min-height: 200rpx;
  resize: vertical;
}

/* 列表样式 */
.list-item {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.06);
  position: relative;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.list-item-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.list-item-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.list-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

/* 状态标签 */
.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-overdue {
  background-color: #f8d7da;
  color: #721c24;
}

.status-high {
  background-color: #f8d7da;
  color: #721c24;
}

.status-medium {
  background-color: #fff3cd;
  color: #856404;
}

.status-low {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  font-size: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4);
  z-index: 1000;
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-primary { color: #4CAF50; }
.text-secondary { color: #666; }
.text-danger { color: #f44336; }
.text-success { color: #4CAF50; }
.text-warning { color: #ff9800; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  justify-content: center; 
  align-items: center; 
}
.flex-between { 
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
}
.flex-column { flex-direction: column; }
.flex-1 { flex: 1; }

.mt-16 { margin-top: 16rpx; }
.mt-24 { margin-top: 24rpx; }
.mb-16 { margin-bottom: 16rpx; }
.mb-24 { margin-bottom: 24rpx; }
.p-16 { padding: 16rpx; }
.p-24 { padding: 24rpx; }

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #ccc;
}