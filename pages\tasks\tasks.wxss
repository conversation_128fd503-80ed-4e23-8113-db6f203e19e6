/* tasks.wxss */
.filter-bar {
  display: flex;
  background: white;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.06);
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.filter-item.active {
  background-color: #4CAF50;
  color: white;
  font-weight: 500;
}

.task-item {
  display: flex;
  align-items: flex-start;
  padding: 32rpx !important;
}

.task-checkbox {
  margin-right: 24rpx;
  margin-top: 8rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
  transition: all 0.3s;
}

.checkbox.checked {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.task-content {
  flex: 1;
}

.list-item-title.completed {
  text-decoration: line-through;
  color: #999;
}

.task-due-date {
  font-size: 24rpx;
  color: #666;
}

.task-due-date.overdue {
  color: #f44336;
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  font-size: 24rpx;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: #f0f8f0;
}

.action-btn.delete {
  color: #f44336;
  background-color: #fef0f0;
}