<!--schedule.wxml-->
<view class="container">
  <!-- 日期选择器 -->
  <view class="date-picker-bar">
    <picker mode="date" value="{{selectedDate}}" bindchange="bindDateChange">
      <view class="date-picker">
        <text class="date-text">{{selectedDateText}}</text>
        <text class="date-icon">📅</text>
      </view>
    </picker>
  </view>

  <!-- 日程列表 -->
  <view wx:if="{{schedules.length > 0}}">
    <view class="schedule-item list-item" wx:for="{{schedules}}" wx:key="id">
      <view class="schedule-time">
        <text class="time-text">{{item.timeText}}</text>
      </view>
      
      <view class="schedule-content" bindtap="goToScheduleDetail" data-id="{{item.id}}">
        <view class="list-item-header">
          <text class="list-item-title">{{item.title}}</text>
          <view class="status-tag status-{{item.category}}">{{item.categoryText}}</view>
        </view>
        
        <text class="list-item-content" wx:if="{{item.description}}">{{item.description}}</text>
        
        <view class="list-item-meta">
          <text class="schedule-location" wx:if="{{item.location}}">📍 {{item.location}}</text>
          <view class="schedule-actions">
            <text class="action-btn" bindtap="editSchedule" data-id="{{item.id}}">编辑</text>
            <text class="action-btn delete" bindtap="deleteSchedule" data-id="{{item.id}}">删除</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <text class="empty-icon">📅</text>
    <text class="empty-text">{{selectedDateText}} 暂无日程</text>
    <text class="empty-desc">点击右下角按钮添加新日程</text>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab" bindtap="goToAddSchedule">+</view>
</view>