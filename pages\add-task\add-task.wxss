/* add-task.wxss */
.container {
  padding: 20rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.input, .textarea, .picker {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  min-height: 80rpx;
}

.textarea {
  min-height: 120rpx;
  resize: none;
}

.picker {
  color: #333;
  display: flex;
  align-items: center;
  min-height: 60rpx;
}

.form-actions {
  margin-top: 60rpx;
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.btn-primary {
  background-color: #007aff;
  color: white;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn:active {
  opacity: 0.8;
}