# 登录问题排查指南

## 常见问题及解决方案

### 1. "获取用户信息失败" 错误

**问题描述：**
```
微信授权登录失败: Error: 获取用户信息失败
```

**可能原因：**
1. 在开发工具中，微信授权接口可能不完全可用
2. 用户拒绝了授权请求
3. 网络连接问题
4. 微信版本过低不支持新接口

**解决方案：**

#### 方案1：使用开发模式快速登录
在开发环境中，页面会自动显示"开发模式快速登录"按钮：
- 点击该按钮可以跳过微信授权直接登录
- 仅在开发环境中可见和可用
- 会创建一个测试用户信息

#### 方案2：使用游客模式
- 点击"以游客身份体验"
- 可以体验应用的基本功能
- 数据仅保存在本地

#### 方案3：真机测试
- 在真实的微信环境中测试
- 确保微信版本支持 `getUserProfile` 接口
- 检查网络连接状态

### 2. 授权弹窗不出现

**问题描述：**
点击登录按钮后没有出现微信授权弹窗

**解决方案：**
1. 确认在真机上测试（开发工具可能不支持）
2. 检查微信版本是否支持授权接口
3. 尝试重启微信小程序

### 3. 登录成功但跳转失败

**问题描述：**
显示登录成功但没有跳转到首页

**解决方案：**
1. 检查首页路径是否正确
2. 查看控制台是否有其他错误
3. 确认 `app.json` 中页面路径配置正确

## 开发环境配置

### 启用开发模式
开发模式会在以下情况自动启用：
- 微信开发者工具中运行
- 小程序版本为 `develop` 或 `trial`

### 开发模式功能
1. **快速登录按钮**：跳过微信授权直接登录
2. **详细日志**：输出更多调试信息
3. **错误容错**：提供默认用户信息作为后备

### 测试流程
1. 在开发工具中打开小程序
2. 进入登录页面
3. 如果看到橙色的"开发模式快速登录"按钮，点击即可
4. 如果没有看到，尝试游客模式或真机测试

## 代码调试

### 查看日志
在控制台中查看以下日志：
```javascript
// 登录流程开始
'开始微信授权登录流程...'

// 会话状态检查
'微信会话状态: true/false'

// 获取登录code
'获取登录code成功: xxx'

// 用户信息获取
'开始智能获取用户信息...'
'使用 getUserProfile 接口获取用户信息'
// 或
'检测到已有授权，使用 getUserInfo 接口'
// 或
'使用默认用户信息'

// 登录成功
'微信授权登录成功: {...}'
```

### 错误处理
代码已经包含了完善的错误处理：
1. **用户拒绝授权**：显示授权说明弹窗
2. **网络错误**：提示检查网络连接
3. **接口不支持**：自动降级到默认用户信息
4. **其他错误**：显示通用错误提示

## 生产环境部署

### 注意事项
1. **移除开发模式代码**（可选）
2. **配置真实的后端接口**
3. **测试真机授权流程**
4. **确保隐私政策和用户协议完整**

### 上线前检查
- [ ] 真机测试微信授权流程
- [ ] 确认用户拒绝授权的处理流程
- [ ] 测试游客模式功能
- [ ] 验证登录状态持久化
- [ ] 检查页面跳转逻辑

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 错误的完整日志
2. 测试环境（开发工具/真机）
3. 微信版本号
4. 操作步骤

这样可以更快地定位和解决问题。
