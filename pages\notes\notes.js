// notes.js
const app = getApp()
const { formatRelativeDate, getTextPreview, showDeleteConfirm, showSuccess } = require('../../utils/common')

Page({
  data: {
    searchKeyword: '',
    notes: [],
    filteredNotes: []
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return
    }
    this.loadNotes()
  },

  onShow() {
    this.loadNotes()
  },

  loadNotes() {
    const notes = wx.getStorageSync('notes') || []
    
    const processedNotes = notes
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      .map(note => ({
        ...note,
        preview: getTextPreview(note.content),
        updatedAtText: formatRelativeDate(note.updatedAt),
        tags: note.tags || []
      }))

    this.setData({ notes: processedNotes })
    this.filterNotes()
  },


  bindSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value })
    this.filterNotes()
  },

  filterNotes() {
    const { notes, searchKeyword } = this.data
    
    if (!searchKeyword.trim()) {
      this.setData({ filteredNotes: notes })
      return
    }

    const keyword = searchKeyword.toLowerCase()
    const filteredNotes = notes.filter(note => {
      return note.title.toLowerCase().includes(keyword) ||
             note.content.toLowerCase().includes(keyword) ||
             (note.tags && note.tags.some(tag => tag.toLowerCase().includes(keyword)))
    })

    this.setData({ filteredNotes })
  },

  editNote(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/add-note/add-note?id=${id}`
    })
  },

  async deleteNote(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    
    const confirmed = await showDeleteConfirm('备忘录')
    if (confirmed) {
      const notes = wx.getStorageSync('notes') || []
      const filteredNotes = notes.filter(note => note.id !== id)
      wx.setStorageSync('notes', filteredNotes)
      this.loadNotes()
      
      showSuccess('备忘录已删除')
    }
  },

  goToNoteDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/note-detail/note-detail?id=${id}`
    })
  },

  goToAddNote() {
    wx.navigateTo({
      url: '/pages/add-note/add-note'
    })
  }
})