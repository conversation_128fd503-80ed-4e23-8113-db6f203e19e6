// index.js
const app = getApp()
const { formatDate, formatTime, formatDateTime, getPriorityText, getStatusText, getWechatUserInfo } = require('../../utils/common')

Page({
  data: {
    userInfo: {},
    todayDate: '',
    stats: {
      totalTasks: 0,
      pendingTasks: 0,
      completedTasks: 0,
      overdueTasks: 0
    },
    todayTasks: [],
    recentSchedules: [],
    loginInfo: {
      code: '',
      openid: '',
      sessionKey: ''
    }
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return
    }
    
    this.setData({
      userInfo: app.globalData.userInfo || {},
      todayDate: formatDate(new Date())
    })
    
    // 微信登录获取code和openid
    this.wxLogin()
    
    // 总是尝试获取最新的用户信息
    this.getUserInfo()
    
    this.loadStats()
    this.loadTodayTasks()
    this.loadRecentSchedules()
  },

  // 微信登录获取登录信息
  wxLogin() {
    const that = this
    wx.login({
      success: (res) => {
        console.log('微信登录成功，获取到code:', res.code)
        
        // 在模拟器中，我们可以模拟openid和session_key
        const mockOpenid = 'mock_openid_' + Date.now()
        const mockSessionKey = 'mock_session_key_' + Date.now()
        
        that.setData({
          'loginInfo.code': res.code,
          'loginInfo.openid': mockOpenid,
          'loginInfo.sessionKey': mockSessionKey
        })
        
        console.log('模拟登录信息:', {
          code: res.code,
          openid: mockOpenid,
          sessionKey: mockSessionKey
        })
        
        // 在真实项目中，这里需要将code发送到后端服务器
        // 后端调用微信接口换取真实的openid和session_key
        // that.sendCodeToServer(res.code)
        
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        })
      },
      fail: (err) => {
        console.error('微信登录失败:', err)
        wx.showToast({
          title: '登录失败',
          icon: 'none',
          duration: 2000
        })
      }
    })
  },

  // 发送code到服务器（真实项目中使用）
  sendCodeToServer(code) {
    // 示例：发送到后端服务器
    /*
    wx.request({
      url: 'https://your-server.com/api/wx-login',
      method: 'POST',
      data: {
        code: code
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            'loginInfo.openid': res.data.openid,
            'loginInfo.sessionKey': res.data.session_key
          })
          console.log('获取到真实openid:', res.data.openid)
        }
      },
      fail: (err) => {
        console.error('服务器登录失败:', err)
      }
    })
    */
  },

  // 获取用户信息
  getUserInfo() {
    // 优先使用全局数据中的用户信息
    if (app.globalData.userInfo && app.globalData.userInfo.nickName) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
      return
    }

    // 其次尝试从本地存储获取
    const localUserInfo = wx.getStorageSync('userInfo')
    if (localUserInfo && localUserInfo.nickName) {
      app.globalData.userInfo = localUserInfo
      this.setData({
        userInfo: localUserInfo
      })
      return
    }

    // 设置默认用户信息
    const defaultUserInfo = { 
      nickName: '智能用户' + Math.floor(Math.random() * 999 + 1),
      avatarUrl: '/images/default-avatar.svg'
    }
    app.globalData.userInfo = defaultUserInfo
    this.setData({
      userInfo: defaultUserInfo
    })
    wx.setStorageSync('userInfo', defaultUserInfo)
  },

  // 点击头像更新用户信息
  async getUserProfile() {
    try {
      const userInfo = await getWechatUserInfo()
      
      // 更新页面数据
      this.setData({
        userInfo: userInfo
      })
      
      // 显示完整的用户信息和登录信息
      console.log('完整用户数据:', {
        userInfo: userInfo,
        loginInfo: this.data.loginInfo
      })
      
      wx.showToast({
        title: '微信用户信息获取成功',
        icon: 'success',
        duration: 2000
      })
      
    } catch (err) {
      console.error('获取微信用户信息失败:', err)
      
      // 如果用户拒绝授权，显示提示
      wx.showModal({
        title: '授权提示', 
        content: '需要获取您的微信头像和昵称用于完善资料显示。\n\n请重新点击并同意授权。',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  // 查看登录信息（调试用）
  showLoginInfo() {
    const loginInfo = this.data.loginInfo
    wx.showModal({
      title: '登录信息',
      content: `Code: ${loginInfo.code}\nOpenID: ${loginInfo.openid}\nSessionKey: ${loginInfo.sessionKey}`,
      showCancel: false
    })
  },

  onShow() {
    this.loadStats()
    this.loadTodayTasks()
    this.loadRecentSchedules()
  },

  loadStats() {
    const tasks = wx.getStorageSync('tasks') || []
    const today = new Date().toDateString()
    
    const stats = {
      totalTasks: tasks.length,
      pendingTasks: tasks.filter(task => task.status === 'pending').length,
      completedTasks: tasks.filter(task => task.status === 'completed').length,
      overdueTasks: tasks.filter(task => {
        if (task.status === 'completed') return false
        const dueDate = new Date(task.dueDate)
        return dueDate < new Date() && dueDate.toDateString() !== today
      }).length
    }
    
    this.setData({ stats })
  },

  loadTodayTasks() {
    const tasks = wx.getStorageSync('tasks') || []
    const today = new Date().toDateString()
    
    const todayTasks = tasks
      .filter(task => new Date(task.dueDate).toDateString() === today)
      .slice(0, 3)
      .map(task => ({
        ...task,
        priorityText: getPriorityText(task.priority),
        statusText: getStatusText(task.status),
        dueTime: formatTime(task.dueDate)
      }))
    
    this.setData({ todayTasks })
  },

  loadRecentSchedules() {
    const schedules = wx.getStorageSync('schedules') || []
    const now = new Date()
    
    const recentSchedules = schedules
      .filter(schedule => new Date(schedule.datetime) >= now)
      .sort((a, b) => new Date(a.datetime) - new Date(b.datetime))
      .slice(0, 3)
      .map(schedule => ({
        ...schedule,
        time: formatDateTime(schedule.datetime)
      }))
    
    this.setData({ recentSchedules })
  },


  // 页面跳转
  goToTasks() {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    })
  },

  goToSchedule() {
    wx.switchTab({
      url: '/pages/schedule/schedule'
    })
  },

  goToTaskDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${id}`
    })
  },

  goToAddTask() {
    wx.navigateTo({
      url: '/pages/add-task/add-task'
    })
  },

  goToAddSchedule() {
    wx.navigateTo({
      url: '/pages/add-schedule/add-schedule'
    })
  },

  goToAddNote() {
    wx.navigateTo({
      url: '/pages/add-note/add-note'
    })
  }
})