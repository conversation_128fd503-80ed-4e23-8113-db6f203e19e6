// index.js
const app = getApp()
const { formatDate, formatTime, formatDateTime, getPriorityText, getStatusText, getWechatUserInfo } = require('../../utils/common')
const { loginManager } = require('../../utils/login-manager')

Page({
  data: {
    userInfo: {},
    todayDate: '',
    stats: {
      totalTasks: 0,
      pendingTasks: 0,
      completedTasks: 0,
      overdueTasks: 0
    },
    todayTasks: [],
    recentSchedules: [],
    loginInfo: {
      code: '',
      openid: '',
      sessionKey: ''
    }
  },

  onLoad() {
    // 检查登录状态
    if (!app.requireLogin()) {
      return
    }
    
    this.setData({
      userInfo: app.globalData.userInfo || {},
      todayDate: formatDate(new Date())
    })
    
    // 微信登录获取code和openid
    this.wxLogin()
    
    // 总是尝试获取最新的用户信息
    this.getUserInfo()
    
    this.loadStats()
    this.loadTodayTasks()
    this.loadRecentSchedules()
  },

  // 微信登录获取登录信息
  wxLogin() {
    const that = this
    wx.login({
      success: (res) => {
        console.log('微信登录成功，获取到code:', res.code)

        if (loginManager.isDevelopment()) {
          // 开发环境：使用模拟数据
          const mockLoginInfo = loginManager.generateMockLoginInfo(res.code)

          that.setData({
            'loginInfo.code': mockLoginInfo.code,
            'loginInfo.openid': mockLoginInfo.openid,
            'loginInfo.sessionKey': mockLoginInfo.sessionKey
          })

          // 保存登录信息
          loginManager.saveLoginInfo(mockLoginInfo)

          console.log('开发环境 - 模拟登录信息:', {
            code: mockLoginInfo.code,
            openid: mockLoginInfo.openid,
            sessionKey: mockLoginInfo.sessionKey,
            environment: mockLoginInfo.environment
          })

          wx.showToast({
            title: `${mockLoginInfo.environment}登录成功`,
            icon: 'success',
            duration: 1500
          })
        } else {
          // 生产环境：调用后端接口
          that.sendCodeToServer(res.code)
        }
      },
      fail: (err) => {
        console.error('微信登录失败:', err)
        wx.showToast({
          title: '获取登录凭证失败',
          icon: 'none',
          duration: 2000
        })
      }
    })
  },

  // 发送code到服务器（生产环境使用）
  sendCodeToServer(code) {
    const that = this

    wx.showLoading({
      title: '正在验证登录...',
      mask: true
    })

    // 调用后端登录接口
    wx.request({
      url: 'https://your-backend-domain.com/api/wechat/login', // 请替换为您的后端域名
      method: 'POST',
      data: {
        code: code,
        userInfo: that.data.userInfo
      },
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading()

        if (res.data && res.data.code === 0) {
          // 登录成功，保存真实的用户信息
          const realLoginInfo = {
            code: code,
            openid: res.data.data.openid,
            sessionKey: res.data.data.session_key,
            isMock: false
          }

          that.setData({
            'loginInfo.code': realLoginInfo.code,
            'loginInfo.openid': realLoginInfo.openid,
            'loginInfo.sessionKey': realLoginInfo.sessionKey
          })

          // 保存登录信息
          loginManager.saveLoginInfo(realLoginInfo)

          console.log('生产环境 - 真实登录信息:', {
            code: realLoginInfo.code,
            openid: realLoginInfo.openid,
            sessionKey: '***', // 不在日志中显示完整的session_key
            environment: loginManager.getEnvironmentType()
          })

          wx.showToast({
            title: '登录验证成功',
            icon: 'success',
            duration: 1500
          })
        } else {
          console.error('后端登录失败:', res.data)
          wx.showToast({
            title: res.data.message || '登录验证失败',
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('网络请求失败:', err)

        // 网络失败时，降级使用本地模拟数据
        const fallbackLoginInfo = {
          code: code,
          openid: 'fallback_openid_' + Date.now(),
          sessionKey: 'fallback_session_key_' + Date.now(),
          isMock: true,
          isFallback: true
        }

        that.setData({
          'loginInfo.code': fallbackLoginInfo.code,
          'loginInfo.openid': fallbackLoginInfo.openid,
          'loginInfo.sessionKey': fallbackLoginInfo.sessionKey
        })

        // 保存降级登录信息
        loginManager.saveLoginInfo(fallbackLoginInfo)

        wx.showToast({
          title: '网络异常，使用离线模式',
          icon: 'none',
          duration: 2000
        })
      }
    })
  },

  // 获取用户信息
  getUserInfo() {
    // 优先使用全局数据中的用户信息
    if (app.globalData.userInfo && app.globalData.userInfo.nickName) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
      return
    }

    // 其次尝试从本地存储获取
    const localUserInfo = wx.getStorageSync('userInfo')
    if (localUserInfo && localUserInfo.nickName) {
      app.globalData.userInfo = localUserInfo
      this.setData({
        userInfo: localUserInfo
      })
      return
    }

    // 设置默认用户信息
    const defaultUserInfo = { 
      nickName: '智能用户' + Math.floor(Math.random() * 999 + 1),
      avatarUrl: '/images/default-avatar.svg'
    }
    app.globalData.userInfo = defaultUserInfo
    this.setData({
      userInfo: defaultUserInfo
    })
    wx.setStorageSync('userInfo', defaultUserInfo)
  },

  // 点击头像更新用户信息
  async getUserProfile() {
    wx.showActionSheet({
      itemList: ['获取真实微信信息', '使用普通授权', '查看当前信息'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.getRealWechatUserInfo()
            break
          case 1:
            this.getNormalWechatUserInfo()
            break
          case 2:
            this.showCurrentUserInfo()
            break
        }
      }
    })
  },

  // 获取真实微信用户信息
  async getRealWechatUserInfo() {
    try {
      wx.showLoading({
        title: '获取真实信息中...',
        mask: true
      })

      // 使用微信授权工具获取真实用户信息
      const userInfo = await wechatAuth.getSmartUserInfo(true)

      wx.hideLoading()

      // 更新页面数据和全局数据
      this.setData({
        userInfo: userInfo
      })

      app.globalData.userInfo = userInfo
      wx.setStorageSync('userInfo', userInfo)

      // 显示结果
      if (userInfo.isReal) {
        wx.showToast({
          title: `欢迎，${userInfo.nickName}！`,
          icon: 'success',
          duration: 2000
        })

        console.log('✅ 获取真实微信用户信息成功:', {
          nickName: userInfo.nickName,
          source: userInfo.source,
          isReal: userInfo.isReal
        })
      } else {
        wx.showToast({
          title: '未能获取真实信息',
          icon: 'none',
          duration: 2000
        })
      }

    } catch (err) {
      wx.hideLoading()
      console.error('获取真实微信用户信息失败:', err)

      wx.showModal({
        title: '获取真实信息失败',
        content: '可能的原因：\n1. 用户拒绝授权\n2. 当前环境不支持\n3. 网络连接问题\n\n建议在真机环境中测试',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  // 获取普通微信用户信息
  async getNormalWechatUserInfo() {
    try {
      const userInfo = await getWechatUserInfo()

      // 更新页面数据
      this.setData({
        userInfo: userInfo
      })

      wx.showToast({
        title: '用户信息更新成功',
        icon: 'success',
        duration: 2000
      })

    } catch (err) {
      console.error('获取微信用户信息失败:', err)

      wx.showModal({
        title: '授权提示',
        content: '需要获取您的微信头像和昵称用于完善资料显示。\n\n请重新点击并同意授权。',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  // 显示当前用户信息
  showCurrentUserInfo() {
    const userInfo = this.data.userInfo
    const content = `昵称: ${userInfo.nickName || '未设置'}
头像: ${userInfo.avatarUrl ? '已设置' : '未设置'}
性别: ${userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '未知'}
地区: ${userInfo.country || '未知'} ${userInfo.province || ''} ${userInfo.city || ''}
语言: ${userInfo.language || '未知'}
类型: ${userInfo.isReal ? '真实信息' : '默认信息'}
来源: ${userInfo.source || '未知'}`

    wx.showModal({
      title: '当前用户信息',
      content: content,
      showCancel: true,
      cancelText: '复制信息',
      confirmText: '确定',
      success: (res) => {
        if (res.cancel) {
          wx.setClipboardData({
            data: content,
            success: () => {
              wx.showToast({
                title: '信息已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 查看登录信息（调试用）
  showLoginInfo() {
    // 使用登录管理器显示信息
    loginManager.showLoginInfoModal()
  },

  onShow() {
    this.loadStats()
    this.loadTodayTasks()
    this.loadRecentSchedules()
  },

  loadStats() {
    const tasks = wx.getStorageSync('tasks') || []
    const today = new Date().toDateString()
    
    const stats = {
      totalTasks: tasks.length,
      pendingTasks: tasks.filter(task => task.status === 'pending').length,
      completedTasks: tasks.filter(task => task.status === 'completed').length,
      overdueTasks: tasks.filter(task => {
        if (task.status === 'completed') return false
        const dueDate = new Date(task.dueDate)
        return dueDate < new Date() && dueDate.toDateString() !== today
      }).length
    }
    
    this.setData({ stats })
  },

  loadTodayTasks() {
    const tasks = wx.getStorageSync('tasks') || []
    const today = new Date().toDateString()
    
    const todayTasks = tasks
      .filter(task => new Date(task.dueDate).toDateString() === today)
      .slice(0, 3)
      .map(task => ({
        ...task,
        priorityText: getPriorityText(task.priority),
        statusText: getStatusText(task.status),
        dueTime: formatTime(task.dueDate)
      }))
    
    this.setData({ todayTasks })
  },

  loadRecentSchedules() {
    const schedules = wx.getStorageSync('schedules') || []
    const now = new Date()
    
    const recentSchedules = schedules
      .filter(schedule => new Date(schedule.datetime) >= now)
      .sort((a, b) => new Date(a.datetime) - new Date(b.datetime))
      .slice(0, 3)
      .map(schedule => ({
        ...schedule,
        time: formatDateTime(schedule.datetime)
      }))
    
    this.setData({ recentSchedules })
  },


  // 页面跳转
  goToTasks() {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    })
  },

  goToSchedule() {
    wx.switchTab({
      url: '/pages/schedule/schedule'
    })
  },

  goToTaskDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${id}`
    })
  },

  goToAddTask() {
    wx.navigateTo({
      url: '/pages/add-task/add-task'
    })
  },

  goToAddSchedule() {
    wx.navigateTo({
      url: '/pages/add-schedule/add-schedule'
    })
  },

  goToAddNote() {
    wx.navigateTo({
      url: '/pages/add-note/add-note'
    })
  }
})