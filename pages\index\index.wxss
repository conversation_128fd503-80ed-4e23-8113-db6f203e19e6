/* index.wxss */
.welcome-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.welcome-header {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.update-hint {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
}

.welcome-title {
  font-size: 40rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255,255,255,0.3);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.stat-card {
  text-align: center;
  padding: 32rpx 16rpx;
}

.welcome-text {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

.task-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.task-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-time {
  font-size: 24rpx;
  color: #999;
}

.schedule-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-time {
  width: 120rpx;
  font-size: 24rpx;
  color: #4CAF50;
  font-weight: 500;
  margin-right: 24rpx;
}

.schedule-content {
  flex: 1;
}

.schedule-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.schedule-desc {
  font-size: 28rpx;
  color: #666;
}

.quick-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 40rpx;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 24rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  min-width: 160rpx;
}

.quick-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.quick-text {
  font-size: 28rpx;
  color: #333;
}

.clear-button {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 16px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  line-height: 1;
}