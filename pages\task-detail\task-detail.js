// task-detail.js
const app = getApp()
const { 
  getPriorityText, 
  getStatusText, 
  formatDate, 
  formatTime, 
  formatDateForInput, 
  formatTimeForInput, 
  isTaskOverdue,
  showError,
  showSuccess,
  showDeleteConfirm
} = require('../../utils/common')

Page({
  data: {
    task: null,
    isEditing: false,
    editForm: {
      title: '',
      description: '',
      priority: 'medium',
      dueDate: '',
      dueTime: ''
    }
  },

  onLoad(options) {
    // 检查登录状态
    if (!app.requireLogin()) {
      return
    }

    const taskId = options.id
    if (taskId) {
      this.loadTask(taskId)
    } else {
      showError('任务不存在')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载任务详情
  loadTask(taskId) {
    const tasks = wx.getStorageSync('tasks') || []
    const task = tasks.find(t => t.id === taskId)
    
    if (task) {
      this.setData({
        task: {
          ...task,
          priorityText: getPriorityText(task.priority),
          statusText: getStatusText(task.status),
          dueDateText: formatDate(task.dueDate),
          dueTimeText: formatTime(task.dueDate),
          isOverdue: isTaskOverdue(task)
        },
        editForm: {
          title: task.title,
          description: task.description || '',
          priority: task.priority,
          dueDate: formatDateForInput(task.dueDate),
          dueTime: formatTimeForInput(task.dueDate)
        }
      })
    } else {
      showError('任务不存在')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 切换任务状态
  toggleTaskStatus() {
    const { task } = this.data
    const newStatus = task.status === 'completed' ? 'pending' : 'completed'
    
    this.updateTaskStatus(task.id, newStatus)
  },

  // 更新任务状态
  updateTaskStatus(taskId, status) {
    const tasks = wx.getStorageSync('tasks') || []
    const taskIndex = tasks.findIndex(t => t.id === taskId)
    
    if (taskIndex !== -1) {
      tasks[taskIndex].status = status
      tasks[taskIndex].completedAt = status === 'completed' ? new Date().toISOString() : null
      
      wx.setStorageSync('tasks', tasks)
      
      // 更新页面数据
      this.setData({
        'task.status': status,
        'task.statusText': getStatusText(status)
      })
      
      showSuccess(status === 'completed' ? '任务已完成' : '任务已重新激活')
    }
  },

  // 开始编辑
  startEdit() {
    this.setData({
      isEditing: true
    })
  },

  // 取消编辑
  cancelEdit() {
    const { task } = this.data
    this.setData({
      isEditing: false,
      editForm: {
        title: task.title,
        description: task.description || '',
        priority: task.priority,
        dueDate: formatDateForInput(task.dueDate),
        dueTime: formatTimeForInput(task.dueDate)
      }
    })
  },

  // 保存编辑
  saveEdit() {
    const { task, editForm } = this.data
    
    if (!editForm.title.trim()) {
      showError('请输入任务标题')
      return
    }

    if (!editForm.dueDate || !editForm.dueTime) {
      showError('请选择截止时间')
      return
    }

    const tasks = wx.getStorageSync('tasks') || []
    const taskIndex = tasks.findIndex(t => t.id === task.id)
    
    if (taskIndex !== -1) {
      const dueDateTime = new Date(`${editForm.dueDate} ${editForm.dueTime}`).toISOString()
      
      tasks[taskIndex] = {
        ...tasks[taskIndex],
        title: editForm.title.trim(),
        description: editForm.description.trim(),
        priority: editForm.priority,
        dueDate: dueDateTime,
        updatedAt: new Date().toISOString()
      }
      
      wx.setStorageSync('tasks', tasks)
      
      // 重新加载任务数据
      this.loadTask(task.id)
      
      this.setData({
        isEditing: false
      })
      
      showSuccess('保存成功')
    }
  },

  // 删除任务
  async deleteTask() {
    const confirmed = await showDeleteConfirm('任务')
    if (confirmed) {
      const { task } = this.data
      const tasks = wx.getStorageSync('tasks') || []
      const filteredTasks = tasks.filter(t => t.id !== task.id)
      
      wx.setStorageSync('tasks', filteredTasks)
      
      showSuccess('删除成功')
      
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 表单输入处理
  onTitleInput(e) {
    this.setData({
      'editForm.title': e.detail.value
    })
  },

  onDescriptionInput(e) {
    this.setData({
      'editForm.description': e.detail.value
    })
  },

  onPriorityChange(e) {
    const priorities = ['low', 'medium', 'high']
    this.setData({
      'editForm.priority': priorities[e.detail.value]
    })
  },

  onDateChange(e) {
    this.setData({
      'editForm.dueDate': e.detail.value
    })
  },

  onTimeChange(e) {
    this.setData({
      'editForm.dueTime': e.detail.value
    })
  },

})