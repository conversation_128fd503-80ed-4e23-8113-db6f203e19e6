// tasks.js
const { formatDueDate, getPriorityTextShort, showDeleteConfirm, showSuccess } = require('../../utils/common')

Page({
  data: {
    currentFilter: 'all',
    tasks: [],
    filteredTasks: [],
    stats: {
      total: 0,
      pending: 0,
      completed: 0
    }
  },

  onLoad() {
    // 获取app实例
    const app = getApp();
    
    // 检查登录状态
    if (!app.requireLogin()) {
      return
    }
    this.loadTasks()
  },

  onShow() {
    this.loadTasks()
  },

  loadTasks() {
    const tasks = wx.getStorageSync('tasks') || []
    const now = new Date()
    
    const processedTasks = tasks.map(task => {
      const dueDate = new Date(task.dueDate)
      const isOverdue = task.status !== 'completed' && dueDate < now
      
      return {
        ...task,
        priorityText: getPriorityTextShort(task.priority),
        dueDateText: formatDueDate(task.dueDate),
        isOverdue,
        status: isOverdue ? 'overdue' : task.status
      }
    }).sort((a, b) => {
      // 排序：未完成的在前，按截止时间排序
      if (a.status === 'completed' && b.status !== 'completed') return 1
      if (a.status !== 'completed' && b.status === 'completed') return -1
      return new Date(a.dueDate) - new Date(b.dueDate)
    })

    const stats = {
      total: tasks.length,
      pending: tasks.filter(task => task.status === 'pending').length,
      completed: tasks.filter(task => task.status === 'completed').length
    }

    this.setData({
      tasks: processedTasks,
      stats
    })
    
    this.filterTasks()
  },

  filterTasks() {
    const { tasks, currentFilter } = this.data
    let filteredTasks = tasks

    if (currentFilter === 'pending') {
      filteredTasks = tasks.filter(task => task.status === 'pending' || task.status === 'overdue')
    } else if (currentFilter === 'completed') {
      filteredTasks = tasks.filter(task => task.status === 'completed')
    }

    this.setData({ filteredTasks })
  },

  setFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterTasks()
  },

  toggleTask(e) {
    const id = e.currentTarget.dataset.id
    const tasks = wx.getStorageSync('tasks') || []
    
    const taskIndex = tasks.findIndex(task => task.id === id)
    if (taskIndex !== -1) {
      tasks[taskIndex].status = tasks[taskIndex].status === 'completed' ? 'pending' : 'completed'
      tasks[taskIndex].completedAt = tasks[taskIndex].status === 'completed' ? new Date().toISOString() : null
      
      wx.setStorageSync('tasks', tasks)
      this.loadTasks()
      
      showSuccess(tasks[taskIndex].status === 'completed' ? '任务已完成' : '任务已恢复')
    }
  },

  async deleteTask(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    
    const confirmed = await showDeleteConfirm('任务')
    if (confirmed) {
      const tasks = wx.getStorageSync('tasks') || []
      const filteredTasks = tasks.filter(task => task.id !== id)
      wx.setStorageSync('tasks', filteredTasks)
      this.loadTasks()
      
      showSuccess('任务已删除')
    }
  },

  editTask(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/add-task/add-task?id=${id}`
    })
  },

  goToTaskDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${id}`
    })
  },

  goToAddTask() {
    wx.navigateTo({
      url: '/pages/add-task/add-task'
    })
  },

})