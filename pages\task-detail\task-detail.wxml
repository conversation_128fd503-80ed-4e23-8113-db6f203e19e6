<!--task-detail.wxml-->
<view class="container" wx:if="{{task}}">
  <!-- 任务状态栏 -->
  <view class="status-bar">
    <view class="status-info">
      <text class="status-text status-{{task.status}}">{{task.statusText}}</text>
      <text class="priority-text priority-{{task.priority}}">{{task.priorityText}}</text>
    </view>
    <button class="status-btn" bindtap="toggleTaskStatus">
      {{task.status === 'completed' ? '重新激活' : '标记完成'}}
    </button>
  </view>

  <!-- 任务详情 -->
  <view class="task-detail" wx:if="{{!isEditing}}">
    <view class="detail-section">
      <view class="section-title">任务标题</view>
      <view class="task-title">{{task.title}}</view>
    </view>

    <view class="detail-section" wx:if="{{task.description}}">
      <view class="section-title">任务描述</view>
      <view class="task-description">{{task.description}}</view>
    </view>

    <view class="detail-section">
      <view class="section-title">截止时间</view>
      <view class="task-due-date {{task.isOverdue ? 'overdue' : ''}}">
        {{task.dueDateText}} {{task.dueTimeText}}
        <text class="overdue-tag" wx:if="{{task.isOverdue && task.status !== 'completed'}}">已逾期</text>
      </view>
    </view>

    <view class="detail-section">
      <view class="section-title">创建时间</view>
      <view class="task-created">{{task.createdAt}}</view>
    </view>

    <view class="detail-section" wx:if="{{task.completedAt}}">
      <view class="section-title">完成时间</view>
      <view class="task-completed">{{task.completedAt}}</view>
    </view>
  </view>

  <!-- 编辑表单 -->
  <view class="edit-form" wx:if="{{isEditing}}">
    <view class="form-group">
      <label class="form-label">任务标题</label>
      <input class="form-input" 
             value="{{editForm.title}}" 
             placeholder="请输入任务标题"
             bindinput="onTitleInput" />
    </view>

    <view class="form-group">
      <label class="form-label">任务描述</label>
      <textarea class="form-textarea" 
                value="{{editForm.description}}" 
                placeholder="请输入任务描述（可选）"
                bindinput="onDescriptionInput"></textarea>
    </view>

    <view class="form-group">
      <label class="form-label">优先级</label>
      <picker class="form-picker" 
              mode="selector" 
              range="{{['低优先级', '中优先级', '高优先级']}}"
              range-key="text"
              value="{{editForm.priority === 'low' ? 0 : editForm.priority === 'medium' ? 1 : 2}}"
              bindchange="onPriorityChange">
        <view class="picker-text">
          {{editForm.priority === 'low' ? '低优先级' : editForm.priority === 'medium' ? '中优先级' : '高优先级'}}
        </view>
      </picker>
    </view>

    <view class="form-group">
      <label class="form-label">截止日期</label>
      <picker class="form-picker" 
              mode="date" 
              value="{{editForm.dueDate}}"
              bindchange="onDateChange">
        <view class="picker-text">{{editForm.dueDate || '请选择日期'}}</view>
      </picker>
    </view>

    <view class="form-group">
      <label class="form-label">截止时间</label>
      <picker class="form-picker" 
              mode="time" 
              value="{{editForm.dueTime}}"
              bindchange="onTimeChange">
        <view class="picker-text">{{editForm.dueTime || '请选择时间'}}</view>
      </picker>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view wx:if="{{!isEditing}}" class="button-group">
      <button class="action-btn edit-btn" bindtap="startEdit">编辑任务</button>
      <button class="action-btn delete-btn" bindtap="deleteTask">删除任务</button>
    </view>
    
    <view wx:if="{{isEditing}}" class="button-group">
      <button class="action-btn cancel-btn" bindtap="cancelEdit">取消</button>
      <button class="action-btn save-btn" bindtap="saveEdit">保存</button>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{!task}}">
  <text>加载中...</text>
</view>