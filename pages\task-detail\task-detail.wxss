/* task-detail.wxss */
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  color: white;
}

.status-pending {
  background-color: #ffc107;
}

.status-completed {
  background-color: #28a745;
}

.status-overdue {
  background-color: #dc3545;
}

.priority-text {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: white;
}

.priority-high {
  background-color: #dc3545;
}

.priority-medium {
  background-color: #ffc107;
}

.priority-low {
  background-color: #28a745;
}

.status-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
}

/* 任务详情 */
.task-detail {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.detail-section {
  margin-bottom: 40rpx;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.task-title {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.5;
}

.task-description {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

.task-due-date {
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.task-due-date.overdue {
  color: #dc3545;
}

.overdue-tag {
  background-color: #dc3545;
  color: white;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.task-created,
.task-completed {
  font-size: 28rpx;
  color: #999;
}

/* 编辑表单 */
.edit-form {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.form-group {
  margin-bottom: 40rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  padding: 25rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 15rpx;
  font-size: 30rpx;
  background-color: #f8f9fa;
}

.form-input:focus {
  border-color: #007bff;
  background-color: white;
}

.form-textarea {
  width: 100%;
  min-height: 150rpx;
  padding: 25rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 15rpx;
  font-size: 30rpx;
  background-color: #f8f9fa;
}

.form-textarea:focus {
  border-color: #007bff;
  background-color: white;
}

.form-picker {
  width: 100%;
  padding: 25rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 15rpx;
  background-color: #f8f9fa;
}

.picker-text {
  font-size: 30rpx;
  color: #333;
}

/* 操作按钮 */
.action-buttons {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 25rpx;
  font-size: 30rpx;
  border: none;
}

.edit-btn {
  background-color: #007bff;
  color: white;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
}

.save-btn {
  background-color: #28a745;
  color: white;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  font-size: 32rpx;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .status-bar {
    flex-direction: column;
    gap: 20rpx;
    align-items: stretch;
  }
  
  .button-group {
    flex-direction: column;
  }
}