/* login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* 头部区域 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.1);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.4;
}

/* 登录按钮区域 */
.login-buttons {
  width: 100%;
  max-width: 600rpx;
  margin-bottom: 80rpx;
}

.login-btn {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50rpx;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-btn::after {
  border: none;
}

.login-btn.loading {
  opacity: 0.7;
}

/* 主要微信登录按钮 */
.primary-wechat-btn {
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  color: #ffffff;
  box-shadow: 0 12rpx 30rpx rgba(7, 193, 96, 0.4);
  height: 110rpx;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 50rpx;
}

.primary-wechat-btn:active {
  transform: translateY(3rpx);
  box-shadow: 0 6rpx 15rpx rgba(7, 193, 96, 0.4);
}

.primary-wechat-btn .btn-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 其他登录方式分割线 */
.other-login-title {
  display: flex;
  align-items: center;
  margin: 40rpx 0 30rpx;
  width: 100%;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  margin: 0 30rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 次要登录按钮 */
.secondary-btn {
  height: 80rpx;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 20rpx;
}

.secondary-btn:active {
  opacity: 0.6;
}

/* 次要微信登录按钮 */
.wechat-secondary-btn {
  background: rgba(7, 193, 96, 0.8);
  color: #ffffff;
  box-shadow: 0 6rpx 15rpx rgba(7, 193, 96, 0.2);
}

/* 手机号登录按钮 */
.phone-btn {
  background: #ffffff;
  color: #333333;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.phone-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 游客模式按钮 */
.guest-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: none;
}

.guest-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 按钮图标和文字 */
.btn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 用户协议 */
.agreement {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-bottom: 60rpx;
  padding: 0 20rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.agreement-link {
  font-size: 24rpx;
  color: #ffffff;
  text-decoration: underline;
  margin: 0 8rpx;
}

/* 底部说明 */
.footer {
  margin-top: auto;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .login-container {
    padding: 40rpx 30rpx 30rpx;
  }
  
  .title {
    font-size: 42rpx;
  }
  
  .subtitle {
    font-size: 26rpx;
  }
  
  .login-btn {
    height: 90rpx;
    font-size: 30rpx;
  }
}