/* login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* 头部区域 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 100rpx;
}

.logo {
  width: 140rpx;
  height: 140rpx;
  margin-bottom: 40rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 52rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  line-height: 1.5;
}

/* 登录区域 */
.login-section {
  width: 100%;
  max-width: 600rpx;
  margin-bottom: 60rpx;
}

/* 微信授权登录按钮 */
.wechat-auth-btn {
  width: 100%;
  height: 120rpx;
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  border-radius: 60rpx;
  border: none;
  box-shadow: 0 16rpx 40rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.wechat-auth-btn::after {
  border: none;
}

.wechat-auth-btn:active {
  transform: translateY(4rpx);
  box-shadow: 0 8rpx 20rpx rgba(7, 193, 96, 0.3);
}

.wechat-auth-btn.loading {
  opacity: 0.8;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.wechat-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 加载动画 */
.loading-icon {
  margin-right: 20rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 登录说明 */
.login-desc {
  text-align: center;
  margin-top: 40rpx;
}

.desc-text {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 10rpx;
}

.desc-detail {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 功能特色展示 */
.features {
  display: flex;
  justify-content: space-around;
  width: 100%;
  max-width: 500rpx;
  margin-bottom: 60rpx;
  padding: 40rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.feature-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

/* 游客模式区域 */
.guest-section {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.guest-tip {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 15rpx;
}

.guest-link {
  font-size: 26rpx;
  color: #ffffff;
  text-decoration: underline;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.guest-link:active {
  background: rgba(255, 255, 255, 0.2);
}

/* 用户协议 */
.agreement {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-top: auto;
  padding: 20rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.agreement-link {
  font-size: 24rpx;
  color: #ffffff;
  text-decoration: underline;
  margin: 0 8rpx;
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.agreement-link:active {
  background: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 400px) {
  .login-container {
    padding: 60rpx 30rpx 30rpx;
  }

  .title {
    font-size: 46rpx;
  }

  .subtitle {
    font-size: 28rpx;
  }

  .wechat-auth-btn {
    height: 110rpx;
  }

  .btn-text {
    font-size: 32rpx;
  }

  .features {
    padding: 30rpx 15rpx;
  }

  .feature-icon {
    font-size: 40rpx;
  }

  .feature-text {
    font-size: 22rpx;
  }
}