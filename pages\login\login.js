// login.js - 微信授权登录实现
const app = getApp()
const { wechatAuth } = require('../../utils/wechat-auth.js')

Page({
  data: {
    isLoading: false,
    authTips: '点击按钮进行微信授权登录',
    isDevelopment: false // 开发模式标识
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/index/index'
      })
      return
    }

    // 检查是否为开发环境
    this.checkDevelopmentMode()

    // 检查微信登录授权状态
    this.checkWechatAuth()

    // 显示欢迎提示
    this.showWelcomeTips()
  },

  // 检查开发模式
  checkDevelopmentMode() {
    // 在开发工具中或者本地调试时启用开发模式
    const accountInfo = wx.getAccountInfoSync()
    const isDev = accountInfo.miniProgram.envVersion === 'develop' ||
                  accountInfo.miniProgram.envVersion === 'trial'

    this.setData({
      isDevelopment: isDev
    })

    if (isDev) {
      console.log('当前为开发环境，启用开发模式功能')
    }
  },

  // 显示欢迎提示
  showWelcomeTips() {
    setTimeout(() => {
      wx.showToast({
        title: '欢迎使用个人事务管理',
        icon: 'none',
        duration: 2000
      })
    }, 500)
  },

  // 微信授权登录（主要登录方式）
  async wechatAuthLogin() {
    if (this.data.isLoading) return

    this.setData({
      isLoading: true,
      authTips: '正在进行微信授权...'
    })

    try {
      wx.showLoading({
        title: '微信授权中...',
        mask: true
      })

      // 使用工具类进行完整的微信登录流程（允许使用默认信息）
      const userInfo = await wechatAuth.login(false)

      wx.hideLoading()

      // 根据是否获取到真实信息显示不同提示
      const successMsg = userInfo.isReal ? '获取真实信息成功！' : '登录成功！'
      wx.showToast({
        title: successMsg,
        icon: 'success',
        duration: 1500
      })

      console.log('微信授权登录成功:', {
        nickName: userInfo.nickName,
        isReal: userInfo.isReal,
        source: userInfo.source
      })

      // 延迟跳转到首页，让用户看到成功提示
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      wx.hideLoading()
      console.error('微信授权登录失败:', error)

      // 根据错误类型显示不同的提示信息
      let errorMsg = '登录失败，请重试'
      let showRetry = true
      let showModal = false

      if (error.message.includes('拒绝授权') || error.message.includes('deny')) {
        errorMsg = '需要授权才能使用完整功能'
        showModal = true
        showRetry = false
      } else if (error.message.includes('网络')) {
        errorMsg = '网络连接失败，请检查网络后重试'
      } else if (error.message.includes('正在进行')) {
        errorMsg = '登录正在进行中，请稍后再试'
        showRetry = false
      } else if (error.message.includes('不支持')) {
        errorMsg = '当前环境不支持授权，尝试使用游客模式'
        showRetry = false
      }

      // 显示错误提示
      if (showModal) {
        this.showAuthGuide()
      } else {
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      }

      // 显示重试提示
      if (showRetry) {
        setTimeout(() => {
          this.setData({
            authTips: '登录失败，点击重新尝试'
          })
        }, 3000)
      } else {
        this.setData({
          authTips: '授权失败，您可以尝试游客模式'
        })
      }

    } finally {
      this.setData({
        isLoading: false
      })
    }
  },

  // 显示授权引导
  showAuthGuide() {
    wx.showModal({
      title: '授权说明',
      content: '为了给您提供更好的服务体验，我们需要获取您的微信头像和昵称用于个性化显示。\n\n请点击"允许"完成授权。',
      showCancel: true,
      cancelText: '稍后再说',
      confirmText: '重新授权',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重新授权
          setTimeout(() => {
            this.wechatAuthLogin()
          }, 500)
        } else {
          // 用户选择稍后，更新提示文字
          this.setData({
            authTips: '您可以随时点击按钮进行授权登录'
          })
        }
      }
    })
  },

  // 真实微信用户信息登录
  async realUserLogin() {
    if (this.data.isLoading) return

    this.setData({
      isLoading: true,
      authTips: '正在获取真实微信信息...'
    })

    try {
      wx.showLoading({
        title: '获取真实信息中...',
        mask: true
      })

      // 强制获取真实的微信用户信息
      const userInfo = await wechatAuth.loginWithRealUserInfo()

      wx.hideLoading()

      // 显示成功信息
      if (userInfo.isReal) {
        wx.showToast({
          title: `欢迎，${userInfo.nickName}！`,
          icon: 'success',
          duration: 2000
        })
      } else {
        wx.showToast({
          title: '获取真实信息失败，使用默认信息',
          icon: 'none',
          duration: 2000
        })
      }

      console.log('真实用户信息登录成功:', {
        nickName: userInfo.nickName,
        isReal: userInfo.isReal,
        source: userInfo.source,
        avatarUrl: userInfo.avatarUrl
      })

      // 延迟跳转到首页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 2000)

    } catch (error) {
      wx.hideLoading()
      console.error('真实用户信息登录失败:', error)

      // 根据错误类型显示不同的提示信息
      let errorMsg = '获取真实信息失败'
      let showModal = false

      if (error.message.includes('拒绝授权')) {
        errorMsg = '需要授权才能获取真实信息'
        showModal = true
      } else if (error.message.includes('真机环境')) {
        errorMsg = '请在真机环境中测试'
        showModal = true
      } else if (error.message.includes('网络')) {
        errorMsg = '网络连接失败，请重试'
      }

      if (showModal) {
        wx.showModal({
          title: '获取真实信息失败',
          content: `${errorMsg}\n\n您可以选择：\n1. 重新尝试授权\n2. 使用普通登录\n3. 游客模式体验`,
          confirmText: '重新尝试',
          cancelText: '普通登录',
          success: (res) => {
            if (res.confirm) {
              // 重新尝试
              setTimeout(() => {
                this.realUserLogin()
              }, 500)
            } else {
              // 使用普通登录
              this.wechatAuthLogin()
            }
          }
        })
      } else {
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      }

      this.setData({
        authTips: '获取真实信息失败，可尝试其他登录方式'
      })

    } finally {
      this.setData({
        isLoading: false
      })
    }
  },

  // 检查微信授权状态
  async checkWechatAuth() {
    try {
      const authSetting = await wechatAuth.getSetting()
      console.log('当前授权状态:', authSetting)

      // 检查用户信息授权状态
      if (authSetting['scope.userInfo']) {
        console.log('用户已授权用户信息')
        this.setData({
          authTips: '检测到已有授权，点击快速登录'
        })
      } else {
        this.setData({
          authTips: '首次使用需要进行微信授权'
        })
      }

      // 检查微信登录会话状态
      const isSessionValid = await wechatAuth.checkSession()
      console.log('微信登录会话有效:', isSessionValid)

      if (!isSessionValid) {
        console.log('微信会话已过期，需要重新登录')
      }

    } catch (error) {
      console.error('检查授权状态失败:', error)
      this.setData({
        authTips: '点击按钮进行微信授权登录'
      })
    }
  },

  // 游客模式登录
  guestLogin() {
    wx.showModal({
      title: '游客模式',
      content: '游客模式下功能有限，数据仅保存在本地，无法云端同步。\n\n建议使用微信登录获得完整体验！',
      confirmText: '继续游客模式',
      cancelText: '微信登录',
      success: (res) => {
        if (res.confirm) {
          // 用户选择继续游客模式
          this.enterGuestMode()
        } else {
          // 用户选择微信登录
          this.wechatAuthLogin()
        }
      }
    })
  },

  // 进入游客模式
  enterGuestMode() {
    wx.showLoading({
      title: '进入游客模式...',
      mask: true
    })

    // 设置游客用户信息
    const guestUserInfo = {
      nickName: '游客用户',
      avatarUrl: '/images/default-avatar.svg',
      isGuest: true,
      loginTime: new Date().getTime(),
      loginType: 'guest'
    }

    // 保存到全局数据和本地存储
    app.globalData.userInfo = guestUserInfo
    app.globalData.isLoggedIn = true
    wx.setStorageSync('userInfo', guestUserInfo)
    wx.setStorageSync('isLoggedIn', true)

    wx.hideLoading()
    wx.showToast({
      title: '已进入游客模式',
      icon: 'success',
      duration: 1500
    })

    // 跳转到首页
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/index/index'
      })
    }, 1500)
  },

  // 显示用户协议
  showUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...\n\n1. 用户权利和义务\n2. 服务条款\n3. 隐私保护\n4. 免责声明',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护...\n\n1. 信息收集\n2. 信息使用\n3. 信息保护\n4. 用户权利',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 开发模式快速登录
  devQuickLogin() {
    if (!this.data.isDevelopment) {
      wx.showToast({
        title: '仅开发环境可用',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '开发模式登录...',
      mask: true
    })

    // 创建开发用户信息
    const devUserInfo = {
      nickName: '开发测试用户',
      avatarUrl: '/images/default-avatar.svg',
      gender: 1,
      country: '中国',
      province: '广东',
      city: '深圳',
      language: 'zh_CN',
      loginTime: new Date().getTime(),
      loginType: 'development',
      isDev: true
    }

    // 保存到全局数据和本地存储
    app.globalData.userInfo = devUserInfo
    app.globalData.isLoggedIn = true
    wx.setStorageSync('userInfo', devUserInfo)
    wx.setStorageSync('isLoggedIn', true)

    wx.hideLoading()
    wx.showToast({
      title: '开发模式登录成功',
      icon: 'success',
      duration: 1500
    })

    // 跳转到首页
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/index/index'
      })
    }, 1500)
  }

})
