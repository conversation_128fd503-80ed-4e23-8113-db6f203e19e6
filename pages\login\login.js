// login.js - 微信登录实现
const app = getApp()
const { wechatAuth } = require('../../utils/wechat-auth.js')

Page({
  data: {
    canIUseGetUserProfile: false,
    isLoading: false,
    loginType: 'wechat' // 默认使用微信登录
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/index/index'
      })
      return
    }
    
    // 检查是否支持新的用户信息接口
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 检查微信登录授权状态
    this.checkWechatAuth()
  },

  // 微信授权登录（传统方式）
  async wechatLogin() {
    if (this.data.isLoading) return
    
    this.setData({
      isLoading: true
    })

    try {
      wx.showLoading({
        title: '正在获取授权...',
        mask: true
      })

      // 使用工具类的智能获取用户信息方法
      const userInfo = await wechatAuth.getSmartUserInfo()
      const code = await wechatAuth.getLoginCode()
      
      // 构建完整的用户信息
      const completeUserInfo = {
        ...userInfo,
        loginTime: new Date().getTime(),
        loginType: 'wechat',
        loginCode: code
      }
      
      // 保存登录信息
      wechatAuth.saveLoginInfo(completeUserInfo)

      wx.hideLoading()
      wx.showToast({
        title: '授权成功',
        icon: 'success',
        duration: 1500
      })

      // 跳转到首页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      wx.hideLoading()
      console.error('微信授权失败:', error)
      
      let errorMsg = '授权失败'
      if (error.message.includes('拒绝授权')) {
        errorMsg = '用户取消了授权'
      }
      
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({
        isLoading: false
      })
    }
  },

  // 手机号登录（需要企业认证）
  async phoneLogin(e) {
    wx.showToast({
      title: '手机号登录需要企业认证',
      icon: 'none',
      duration: 2000
    })
    // 暂时禁用手机号登录，因为需要企业认证
    // 如果需要开启，请参考微信小程序开发文档
  },

  // 检查微信授权状态
  async checkWechatAuth() {
    try {
      const authSetting = await wechatAuth.getSetting()
      console.log('当前授权状态:', authSetting)
      
      // 检查用户信息授权状态
      if (authSetting['scope.userInfo']) {
        console.log('用户已授权用户信息')
      }
      
      // 检查微信登录会话状态
      const isSessionValid = await wechatAuth.checkSession()
      console.log('微信登录会话有效:', isSessionValid)
      
    } catch (error) {
      console.error('检查授权状态失败:', error)
    }
  },

  // 一键微信登录（推荐方式）- 使用工具类
  async quickWechatLogin() {
    if (this.data.isLoading) return
    
    this.setData({
      isLoading: true
    })

    try {
      wx.showLoading({
        title: '正在登录...',
        mask: true
      })

      // 使用工具类进行登录
      const userInfo = await wechatAuth.login()

      wx.hideLoading()
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })

      // 跳转到首页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 1500)

    } catch (error) {
      wx.hideLoading()
      console.error('微信登录失败:', error)
      
      // 根据错误类型显示不同的提示
      let errorMsg = '登录失败'
      if (error.message.includes('拒绝授权')) {
        errorMsg = '需要授权才能使用完整功能'
      } else if (error.message.includes('网络')) {
        errorMsg = '网络连接失败，请检查网络'
      } else if (error.message.includes('正在进行')) {
        errorMsg = '登录正在进行中，请稍后'
      }
      
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({
        isLoading: false
      })
    }
  },

  // 获取授权设置
  getSetting() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: resolve,
        fail: reject
      })
    })
  },

  // 游客模式
  guestLogin() {
    wx.showModal({
      title: '游客模式',
      content: '游客模式功能有限，无法同步数据。建议使用微信登录获得完整体验！',
      confirmText: '继续游客模式',
      cancelText: '微信登录',
      success: (res) => {
        if (res.confirm) {
          // 设置游客用户信息
          const guestUserInfo = {
            nickName: '游客用户',
            avatarUrl: '/images/default-avatar.svg',
            isGuest: true,
            loginTime: new Date().getTime(),
            loginType: 'guest'
          }
          
          app.globalData.userInfo = guestUserInfo
          app.globalData.isLoggedIn = true
          wx.setStorageSync('userInfo', guestUserInfo)
          wx.setStorageSync('isLoggedIn', true)
          
          wx.reLaunch({
            url: '/pages/index/index'
          })
        } else {
          // 用户选择微信登录
          this.quickWechatLogin()
        }
      }
    })
  }
})
