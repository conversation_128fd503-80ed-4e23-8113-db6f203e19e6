<!--add-note.wxml-->
<view class="container">
  <form bindsubmit="submitNote">
    <view class="card">
      <view class="card-header">
        <text class="card-title">{{isEdit ? '编辑备忘录' : '添加备忘录'}}</text>
      </view>

      <view class="form-group">
        <view class="form-label">标题 *</view>
        <input class="form-input" name="title" value="{{formData.title}}" placeholder="请输入备忘录标题" maxlength="50" />
      </view>

      <view class="form-group">
        <view class="form-label">内容 *</view>
        <textarea class="form-input form-textarea content-textarea" name="content" value="{{formData.content}}" placeholder="请输入备忘录内容..." maxlength="2000"></textarea>
      </view>

      <view class="form-group">
        <view class="form-label">标签</view>
        <view class="tag-input-container">
          <input class="tag-input" value="{{newTag}}" bindinput="bindTagInput" placeholder="输入标签后按回车添加" confirm-type="done" bindconfirm="addTag" />
          <button class="tag-add-btn" bindtap="addTag" size="mini">添加</button>
        </view>
        <view class="tags-container" wx:if="{{formData.tags.length > 0}}">
          <view class="tag-item" wx:for="{{formData.tags}}" wx:key="*this">
            <text>{{item}}</text>
            <text class="tag-remove" bindtap="removeTag" data-tag="{{item}}">×</text>
          </view>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">重要程度</view>
        <view class="importance-options">
          <view class="importance-option {{formData.importance === 'low' ? 'active low' : ''}}" bindtap="selectImportance" data-importance="low">
            普通
          </view>
          <view class="importance-option {{formData.importance === 'medium' ? 'active medium' : ''}}" bindtap="selectImportance" data-importance="medium">
            重要
          </view>
          <view class="importance-option {{formData.importance === 'high' ? 'active high' : ''}}" bindtap="selectImportance" data-importance="high">
            非常重要
          </view>
        </view>
      </view>

      <view class="form-actions">
        <button class="btn btn-secondary" bindtap="goBack">取消</button>
        <button class="btn btn-primary" form-type="submit">{{isEdit ? '更新' : '保存'}}</button>
      </view>
    </view>
  </form>
</view>