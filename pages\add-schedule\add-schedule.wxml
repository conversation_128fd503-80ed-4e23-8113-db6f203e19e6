<!--add-schedule.wxml-->
<view class="container">
  <form bindsubmit="submitSchedule">
    <view class="card">
      <view class="card-header">
        <text class="card-title">{{isEdit ? '编辑日程' : '添加日程'}}</text>
      </view>

      <view class="form-group">
        <view class="form-label">日程标题 *</view>
        <input class="form-input" name="title" value="{{formData.title}}" placeholder="请输入日程标题" maxlength="50" />
      </view>

      <view class="form-group">
        <view class="form-label">日程描述</view>
        <textarea class="form-input form-textarea" name="description" value="{{formData.description}}" placeholder="请输入日程描述" maxlength="200"></textarea>
      </view>

      <view class="form-group">
        <view class="form-label">日期 *</view>
        <picker mode="date" value="{{formData.date}}" start="{{today}}" bindchange="bindDateChange">
          <view class="form-input picker">{{formData.date || '请选择日期'}}</view>
        </picker>
      </view>

      <view class="form-group">
        <view class="form-label">时间 *</view>
        <picker mode="time" value="{{formData.time}}" bindchange="bindTimeChange">
          <view class="form-input picker">{{formData.time || '请选择时间'}}</view>
        </picker>
      </view>

      <view class="form-group">
        <view class="form-label">地点</view>
        <input class="form-input" name="location" value="{{formData.location}}" placeholder="请输入地点" maxlength="100" />
      </view>

      <view class="form-group">
        <view class="form-label">分类</view>
        <view class="category-options">
          <view class="category-option {{formData.category === 'work' ? 'active work' : ''}}" bindtap="selectCategory" data-category="work">
            工作
          </view>
          <view class="category-option {{formData.category === 'personal' ? 'active personal' : ''}}" bindtap="selectCategory" data-category="personal">
            个人
          </view>
          <view class="category-option {{formData.category === 'meeting' ? 'active meeting' : ''}}" bindtap="selectCategory" data-category="meeting">
            会议
          </view>
          <view class="category-option {{formData.category === 'other' ? 'active other' : ''}}" bindtap="selectCategory" data-category="other">
            其他
          </view>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">提醒设置</view>
        <view class="reminder-options">
          <view class="reminder-option {{formData.reminder === 'none' ? 'active' : ''}}" bindtap="selectReminder" data-reminder="none">
            不提醒
          </view>
          <view class="reminder-option {{formData.reminder === '15min' ? 'active' : ''}}" bindtap="selectReminder" data-reminder="15min">
            15分钟前
          </view>
          <view class="reminder-option {{formData.reminder === '1hour' ? 'active' : ''}}" bindtap="selectReminder" data-reminder="1hour">
            1小时前
          </view>
          <view class="reminder-option {{formData.reminder === '1day' ? 'active' : ''}}" bindtap="selectReminder" data-reminder="1day">
            1天前
          </view>
        </view>
      </view>

      <view class="form-actions">
        <button class="btn btn-secondary" bindtap="goBack">取消</button>
        <button class="btn btn-primary" form-type="submit">{{isEdit ? '更新' : '保存'}}</button>
      </view>
    </view>
  </form>
</view>