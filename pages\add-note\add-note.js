// add-note.js
const { generateId, validateRequired, showSuccess, showError } = require('../../utils/common')

Page({
  data: {
    isEdit: false,
    noteId: null,
    newTag: '',
    formData: {
      title: '',
      content: '',
      tags: []
    }
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ 
        isEdit: true, 
        noteId: options.id 
      })
      this.loadNote(options.id)
    }

    wx.setNavigationBarTitle({
      title: options.id ? '编辑备忘录' : '添加备忘录'
    })
  },

  loadNote(id) {
    const notes = wx.getStorageSync('notes') || []
    const note = notes.find(n => n.id === id)
    
    if (note) {
      this.setData({
        formData: {
          title: note.title,
          content: note.content || '',
          tags: note.tags || []
        }
      })
    }
  },

  bindTagInput(e) {
    this.setData({
      newTag: e.detail.value
    })
  },

  addTag() {
    const { newTag, formData } = this.data
    if (!newTag.trim()) return

    if (formData.tags.includes(newTag.trim())) {
      showError('标签已存在')
      return
    }

    this.setData({
      'formData.tags': [...formData.tags, newTag.trim()],
      newTag: ''
    })
  },

  removeTag(e) {
    const tag = e.currentTarget.dataset.tag
    const tags = this.data.formData.tags.filter(t => t !== tag)
    this.setData({
      'formData.tags': tags
    })
  },

  submitNote(e) {
    const formData = e.detail.value
    const { tags } = this.data.formData

    // 验证必填字段
    if (!validateRequired(formData.title, '备忘录标题')) {
      return
    }

    // 构建备忘录对象
    const note = {
      id: this.data.isEdit ? this.data.noteId : generateId(),
      title: formData.title.trim(),
      content: formData.content.trim(),
      tags,
      createdAt: this.data.isEdit ? undefined : new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // 保存备忘录
    const notes = wx.getStorageSync('notes') || []
    
    if (this.data.isEdit) {
      const index = notes.findIndex(n => n.id === this.data.noteId)
      if (index !== -1) {
        notes[index] = { ...notes[index], ...note }
      }
    } else {
      note.createdAt = new Date().toISOString()
      notes.push(note)
    }

    wx.setStorageSync('notes', notes)

    showSuccess(this.data.isEdit ? '备忘录已更新' : '备忘录已添加')

    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  },

  goBack() {
    wx.navigateBack()
  }
})