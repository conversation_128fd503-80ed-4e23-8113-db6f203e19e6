// add-task.js
const { generateId, validateRequired, showSuccess, showError } = require('../../utils/common')

Page({
  data: {
    task: {
      title: '',
      description: '',
      priority: 'medium',
      dueDate: '',
      dueTime: '09:00'
    },
    isEdit: false,
    taskId: null,
    priorityOptions: [
      { value: 'high', label: '高优先级', color: '#ff4757' },
      { value: 'medium', label: '中优先级', color: '#ffa502' },
      { value: 'low', label: '低优先级', color: '#2ed573' }
    ]
  },

  onLoad(options) {
    // 获取app实例
    const app = getApp();
    
    // 检查登录状态
    if (!app.requireLogin()) {
      return
    }

    if (options.id) {
      this.setData({ 
        isEdit: true, 
        taskId: options.id 
      })
      this.loadTask(options.id)
    } else {
      // 设置默认日期为今天
      const today = new Date()
      const dateStr = today.toISOString().split('T')[0]
      this.setData({
        'task.dueDate': dateStr
      })
    }
  },

  loadTask(id) {
    const tasks = wx.getStorageSync('tasks') || []
    const task = tasks.find(t => t.id === id)
    
    if (task) {
      const dueDate = new Date(task.dueDate)
      const dateStr = dueDate.toISOString().split('T')[0]
      const timeStr = dueDate.toTimeString().slice(0, 5)
      
      this.setData({
        task: {
          title: task.title,
          description: task.description || '',
          priority: task.priority,
          dueDate: dateStr,
          dueTime: timeStr
        }
      })
    }
  },

  onTitleInput(e) {
    this.setData({
      'task.title': e.detail.value
    })
  },

  onDescriptionInput(e) {
    this.setData({
      'task.description': e.detail.value
    })
  },

  onPriorityChange(e) {
    this.setData({
      'task.priority': e.currentTarget.dataset.priority
    })
  },

  onDateChange(e) {
    this.setData({
      'task.dueDate': e.detail.value
    })
  },

  onTimeChange(e) {
    this.setData({
      'task.dueTime': e.detail.value
    })
  },

  saveTask() {
    const { task, isEdit, taskId } = this.data
    
    if (!validateRequired(task.title, '任务标题')) {
      return
    }

    if (!task.dueDate) {
      showError('请选择截止日期')
      return
    }

    const dueDateTime = new Date(`${task.dueDate}T${task.dueTime}:00`)
    
    const taskData = {
      id: isEdit ? taskId : generateId(),
      title: task.title.trim(),
      description: task.description.trim(),
      priority: task.priority,
      dueDate: dueDateTime.toISOString(),
      status: 'pending',
      createdAt: isEdit ? undefined : new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    const tasks = wx.getStorageSync('tasks') || []
    
    if (isEdit) {
      const index = tasks.findIndex(t => t.id === taskId)
      if (index !== -1) {
        // 保留原有的创建时间和状态
        taskData.createdAt = tasks[index].createdAt
        taskData.status = tasks[index].status
        taskData.completedAt = tasks[index].completedAt
        tasks[index] = taskData
      }
    } else {
      tasks.push(taskData)
    }

    wx.setStorageSync('tasks', tasks)
    
    showSuccess(isEdit ? '任务已更新' : '任务已添加')

    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  },

  cancel() {
    wx.navigateBack()
  }
})