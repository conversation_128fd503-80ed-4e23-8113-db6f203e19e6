# 二维码用户信息格式说明

## 功能说明
程序启动时会提示用户扫描二维码获取用户信息，如果扫描成功，将使用二维码中的用户信息；如果扫描失败或用户选择跳过，则使用默认的微信授权方式获取用户信息。

## 二维码数据格式
二维码应包含JSON格式的用户信息数据：

```json
{
  "nickName": "用户昵称",
  "avatarUrl": "头像URL地址",
  "gender": 1,
  "city": "城市",
  "province": "省份",
  "country": "国家"
}
```

## 必需字段
- `nickName`: 用户昵称（必需）

## 可选字段
- `avatarUrl`: 用户头像URL
- `gender`: 性别（0-未知，1-男，2-女）
- `city`: 城市
- `province`: 省份
- `country`: 国家

## 使用示例
创建包含以下内容的二维码：
```json
{
  "nickName": "张三",
  "avatarUrl": "https://example.com/avatar.jpg",
  "gender": 1,
  "city": "北京",
  "province": "北京",
  "country": "中国"
}
```

## 注意事项
1. 二维码数据必须是有效的JSON格式
2. 至少需要包含`nickName`字段
3. 如果二维码格式不正确，程序会自动回退到默认的用户信息获取方式