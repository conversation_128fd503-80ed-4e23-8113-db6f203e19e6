/* notes.wxss */
.search-bar {
  position: relative;
  background: white;
  border-radius: 12rpx;
  margin: 32rpx 0 24rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.06);
}

.search-input {
  width: 100%;
  padding: 32rpx 80rpx 32rpx 32rpx;
  font-size: 32rpx;
  border: none;
  background: transparent;
  box-sizing: border-box;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

.note-item {
  cursor: pointer;
  transition: all 0.3s;
}

.note-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.12);
}

.note-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  font-size: 24rpx;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: #f0f8f0;
}

.action-btn.delete {
  color: #f44336;
  background-color: #fef0f0;
}

.note-date {
  font-size: 24rpx;
  color: #999;
}

.note-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.note-tags .tag-item {
  font-size: 20rpx;
  background-color: #e8f5e8;
  color: #4CAF50;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.list-item-content {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}