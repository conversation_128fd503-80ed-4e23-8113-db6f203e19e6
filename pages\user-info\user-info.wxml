<!--user-info.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <text class="page-title">设置用户信息</text>
  </view>

  <!-- 用户信息表单卡片 -->
  <view class="card form-card">
    <view class="form-section">
      <view class="form-group">
        <text class="form-label">头像</text>
        <view class="avatar-section">
          <button 
            class="avatar-btn" 
            open-type="chooseAvatar" 
            bind:chooseavatar="onChooseAvatar"
          >
            <image 
              class="avatar-preview" 
              src="{{userInfo.avatarUrl}}" 
              mode="aspectFill"
            ></image>
            <view class="avatar-overlay">
              <text class="avatar-text">点击更换</text>
            </view>
          </button>
        </view>
        <view class="avatar-actions">
          <button class="btn btn-secondary btn-small" bindtap="chooseAvatarFallback">
            📷 从相册选择
          </button>
        </view>
        <text class="form-desc">点击头像或使用下方按钮选择头像图片</text>
      </view>

      <view class="form-group">
        <text class="form-label">昵称</text>
        <input 
          class="form-input nickname-input"
          type="nickname"
          placeholder="请输入昵称"
          value="{{userInfo.nickName}}"
          bindinput="onNicknameInput"
          bindblur="onNicknameBlur"
          maxlength="20"
        />
        <text class="form-desc">设置您的显示昵称，最多20个字符</text>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="form-actions">
      <button class="btn btn-primary save-btn" bindtap="saveUserInfo">
        保存用户信息
      </button>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="card tip-card">
    <view class="tip-content">
      <text class="tip-icon">💡</text>
      <view class="tip-text">
        <text class="tip-title">温馨提示</text>
        <text class="tip-desc">您可以随时在个人中心修改头像和昵称信息。头像和昵称仅在本地存储，不会上传到服务器。</text>
      </view>
    </view>
  </view>
</view>
