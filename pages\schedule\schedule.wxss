/* schedule.wxss */
.date-picker-bar {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.06);
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
}

.date-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.date-icon {
  font-size: 32rpx;
}

.schedule-item {
  display: flex;
  align-items: flex-start;
  padding: 32rpx !important;
}

.schedule-time {
  width: 120rpx;
  margin-right: 24rpx;
  text-align: center;
}

.time-text {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 600;
  background-color: #f0f8f0;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
}

.schedule-content {
  flex: 1;
}

.schedule-location {
  font-size: 24rpx;
  color: #666;
}

.schedule-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  font-size: 24rpx;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: #f0f8f0;
}

.action-btn.delete {
  color: #f44336;
  background-color: #fef0f0;
}

.status-work {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-personal {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.status-meeting {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-other {
  background-color: #e8f5e8;
  color: #4caf50;
}