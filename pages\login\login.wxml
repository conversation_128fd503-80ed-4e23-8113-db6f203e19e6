<!--login.wxml-->
<view class="login-container">
  <!-- 头部logo和标题 -->
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">个人事务管理</text>
    <text class="subtitle">让生活更有序，让工作更高效</text>
  </view>

  <!-- 登录按钮区域 -->
  <view class="login-buttons">
    
    <!-- 主要微信登录按钮 -->
    <button 
      class="login-btn primary-wechat-btn {{isLoading ? 'loading' : ''}}"
      bindtap="quickWechatLogin"
      disabled="{{isLoading}}"
    >
      <image class="btn-icon" src="/images/wechat-icon.png" wx:if="{{!isLoading}}"></image>
      <text class="btn-text">{{isLoading ? '登录中...' : '微信一键登录'}}</text>
    </button>

    <!-- 其他登录方式标题 -->
    <view class="other-login-title">
      <text class="divider-line"></text>
      <text class="divider-text">其他登录方式</text>
      <text class="divider-line"></text>
    </view>

    <!-- 传统微信登录按钮 -->
    <button 
      class="login-btn secondary-btn wechat-secondary-btn"
      bindtap="wechatLogin"
      disabled="{{isLoading}}"
    >
      <image class="btn-icon" src="/images/wechat-icon.png"></image>
      <text class="btn-text">微信授权登录</text>
    </button>

    <!-- 手机号登录按钮（需要企业认证） -->
    <button 
      class="login-btn secondary-btn phone-btn"
      open-type="getPhoneNumber"
      bindgetphonenumber="phoneLogin"
      disabled="{{isLoading}}"
    >
      <image class="btn-icon" src="/images/phone-icon.png"></image>
      <text class="btn-text">手机号登录</text>
    </button>

    <!-- 游客模式 -->
    <button 
      class="login-btn secondary-btn guest-btn"
      bindtap="guestLogin"
      disabled="{{isLoading}}"
    >
      <image class="btn-icon" src="/images/guest-icon.png"></image>
      <text class="btn-text">游客模式</text>
    </button>
  </view>

  <!-- 用户协议 -->
  <view class="agreement">
    <text class="agreement-text">登录即表示同意</text>
    <text class="agreement-link">《用户协议》</text>
    <text class="agreement-text">和</text>
    <text class="agreement-link">《隐私政策》</text>
  </view>

  <!-- 底部说明 -->
  <view class="footer">
    <text class="footer-text">安全登录，保护您的隐私</text>
  </view>
</view>