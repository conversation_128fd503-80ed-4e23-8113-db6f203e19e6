<!--login.wxml-->
<view class="login-container">
  <!-- 头部logo和标题 -->
  <view class="header">
    <image class="logo" src="/images/default-avatar.svg" mode="aspectFit"></image>
    <text class="title">个人事务管理</text>
    <text class="subtitle">让生活更有序，让工作更高效</text>
  </view>

  <!-- 微信授权登录区域 -->
  <view class="login-section">
    <!-- 微信授权登录按钮 -->
    <button
      class="wechat-auth-btn {{isLoading ? 'loading' : ''}}"
      bindtap="wechatAuthLogin"
      disabled="{{isLoading}}"
    >
      <view class="btn-content">
        <image class="wechat-icon" src="/images/default-avatar.svg" wx:if="{{!isLoading}}"></image>
        <view class="loading-icon" wx:if="{{isLoading}}">
          <view class="loading-spinner"></view>
        </view>
        <text class="btn-text">{{isLoading ? '授权登录中...' : '微信授权登录'}}</text>
      </view>
    </button>

    <!-- 登录说明 -->
    <view class="login-desc">
      <text class="desc-text">使用微信授权登录，安全便捷</text>
      <text class="desc-detail">获取您的微信头像和昵称用于个性化显示</text>
    </view>

    <!-- 真实用户信息登录按钮 -->
    <button
      class="real-user-btn {{isLoading ? 'loading' : ''}}"
      bindtap="realUserLogin"
      disabled="{{isLoading}}"
    >
      <view class="btn-content">
        <text class="real-btn-icon">👤</text>
        <text class="btn-text">{{isLoading ? '获取真实信息中...' : '获取真实微信信息'}}</text>
      </view>
    </button>
  </view>

  <!-- 功能特色展示 -->
  <view class="features">
    <view class="feature-item">
      <view class="feature-icon">📝</view>
      <text class="feature-text">任务管理</text>
    </view>
    <view class="feature-item">
      <view class="feature-icon">📅</view>
      <text class="feature-text">日程安排</text>
    </view>
    <view class="feature-item">
      <view class="feature-icon">📋</view>
      <text class="feature-text">备忘录</text>
    </view>
  </view>

  <!-- 游客模式入口 -->
  <view class="guest-section">
    <text class="guest-tip">暂不登录？</text>
    <text class="guest-link" bindtap="guestLogin">以游客身份体验</text>
  </view>

  <!-- 开发模式快速登录 -->
  <view class="dev-section" wx:if="{{isDevelopment}}">
    <text class="dev-tip">开发模式：</text>
    <text class="dev-link" bindtap="devQuickLogin">快速登录</text>
  </view>

  <!-- 用户协议 -->
  <view class="agreement">
    <text class="agreement-text">登录即表示同意</text>
    <text class="agreement-link" bindtap="showUserAgreement">《用户协议》</text>
    <text class="agreement-text">和</text>
    <text class="agreement-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
  </view>
</view>