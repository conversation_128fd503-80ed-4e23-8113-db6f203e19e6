<!--notes.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <input class="search-input" placeholder="搜索备忘录..." value="{{searchKeyword}}" bindinput="bindSearchInput" />
    <text class="search-icon">🔍</text>
  </view>

  <!-- 备忘录列表 -->
  <view wx:if="{{filteredNotes.length > 0}}">
    <view class="note-item list-item" wx:for="{{filteredNotes}}" wx:key="id" bindtap="goToNoteDetail" data-id="{{item.id}}">
      <view class="list-item-header">
        <text class="list-item-title">{{item.title}}</text>
        <view class="note-actions">
          <text class="action-btn" bindtap="editNote" data-id="{{item.id}}">编辑</text>
          <text class="action-btn delete" bindtap="deleteNote" data-id="{{item.id}}">删除</text>
        </view>
      </view>
      
      <text class="list-item-content">{{item.preview}}</text>
      
      <view class="list-item-meta">
        <text class="note-date">{{item.updatedAtText}}</text>
        <view class="note-tags" wx:if="{{item.tags.length > 0}}">
          <text class="tag-item" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <text class="empty-icon">📝</text>
    <text class="empty-text">{{searchKeyword ? '没有找到相关备忘录' : '暂无备忘录'}}</text>
    <text class="empty-desc" wx:if="{{!searchKeyword}}">点击右下角按钮添加新备忘录</text>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab" bindtap="goToAddNote">+</view>
</view>